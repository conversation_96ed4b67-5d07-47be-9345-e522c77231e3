import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import Func<PERSON>nimation
from scipy.stats import multivariate_normal
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA

# --- Configuration Parameters ---
# These parameters are based on the architecture document and our discussion.
# They are simplified for a 2D visualization.
D_CONCEPT = 2  # Dimensionality of concept vectors (2D for visualization)
N_CONCEPTS = 10 # Number of initial concepts
N_ATTRACTORS = 3 # Number of attractors (knowledge domains)
N_GMM_COMPONENTS = 2 # Number of sub-concepts within each attractor domain

# Simulation parameters
N_STEPS = 200 # Total simulation steps
ETA = 0.05 # Learning rate / step size for motion
GRAPH_FORCE_STRENGTH = 0.02 # Strength of connections between concepts
ATTRACTOR_FORCE_STRENGTH = 0.1 # Strength of attractor pull

# Dynamic event thresholds from the architecture document
TAU_FISSION = 1.5 # Threshold for splitting a concept (based on velocity)
TAU_FUSION = 0.1 # Threshold for merging two concepts (based on distance)

class Concept:
    """Represents a single concept in the manifold."""
    def __init__(self, concept_id, position):
        self.id = concept_id
        self.pos = np.array(position, dtype=float)
        self.vel = np.zeros(D_CONCEPT)
        self.force = np.zeros(D_CONCEPT)

    def __repr__(self):
        return f"Concept({self.id}, pos={self.pos})"

class Attractor:
    """Represents a domain of knowledge as a Gaussian Mixture Model (GMM)."""
    def __init__(self, n_components=N_GMM_COMPONENTS):
        # Initialize a random GMM to represent the domain
        # In a real system, these would be learned parameters.
        means = np.random.rand(n_components, D_CONCEPT) * 10 - 5
        covariances = [np.eye(D_CONCEPT) * (np.random.rand() * 2 + 0.5) for _ in range(n_components)]
        weights = np.ones(n_components) / n_components

        self.gmm = GaussianMixture(n_components=n_components, covariance_type='full')
        self.gmm.weights_ = weights
        self.gmm.means_ = means
        self.gmm.covariances_ = covariances
        
        # FIX: Force the GMM to compute its precision matrices.
        # By fitting it to its own means, we trigger the necessary internal
        # setup that creates the 'precisions_cholesky_' attribute without
        # changing the GMM's parameters.
        self.gmm.fit(self.gmm.means_)


    def calculate_force_gradient(self, position, epsilon=1e-5):
        """
        Calculates the force (gradient of log probability) on a position.
        This pulls the concept towards regions of higher probability in the domain.
        """
        grad = np.zeros(D_CONCEPT)
        for i in range(D_CONCEPT):
            pos_plus = position.copy()
            pos_plus[i] += epsilon
            pos_minus = position.copy()
            pos_minus[i] -= epsilon

            log_prob_plus = self.gmm.score_samples(pos_plus.reshape(1, -1))
            log_prob_minus = self.gmm.score_samples(pos_minus.reshape(1, -1))

            grad[i] = (log_prob_plus[0] - log_prob_minus[0]) / (2 * epsilon)
        return grad

class Simulation:
    """Orchestrates the entire thought simulation process."""
    def __init__(self):
        # 1. Conceptualization Phase (Simplified)
        self.concepts = {i: Concept(i, np.random.rand(D_CONCEPT) * 8 - 4) for i in range(N_CONCEPTS)}
        self.concept_graph = {i: [(i + 1) % N_CONCEPTS, (i - 1 + N_CONCEPTS) % N_CONCEPTS] for i in range(N_CONCEPTS)}

        # Create the Constraint Field
        self.attractors = [Attractor() for _ in range(N_ATTRACTORS)]
        self.next_concept_id = N_CONCEPTS

    def _apply_graph_forces(self):
        """Applies forces based on the relationships in the ConceptGraph."""
        for cid, concept in self.concepts.items():
            for neighbor_id in self.concept_graph.get(cid, []):
                if neighbor_id in self.concepts:
                    neighbor = self.concepts[neighbor_id]
                    direction = neighbor.pos - concept.pos
                    concept.force += direction * GRAPH_FORCE_STRENGTH

    def _apply_attractor_forces(self):
        """Applies forces from the semantic/structural attractor domains."""
        for concept in self.concepts.values():
            total_attractor_force = np.zeros(D_CONCEPT)
            for attractor in self.attractors:
                total_attractor_force += attractor.calculate_force_gradient(concept.pos)
            concept.force += total_attractor_force * ATTRACTOR_FORCE_STRENGTH

    def _update_positions(self):
        """Updates concept positions based on the total calculated force."""
        for concept in self.concepts.values():
            force_magnitude = np.linalg.norm(concept.force)
            if force_magnitude > 2.0:
                 concept.force = concept.force / force_magnitude * 2.0

            concept.vel = concept.force
            concept.pos += concept.vel * ETA
            concept.force = np.zeros(D_CONCEPT)

    def _handle_dynamic_events(self):
        """Handles Fission and Fusion events."""
        # Fission
        fission_events = []
        for cid, concept in list(self.concepts.items()):
            if np.linalg.norm(concept.vel) > TAU_FISSION:
                fission_events.append(cid)

        for cid in fission_events:
            if cid not in self.concepts: continue
            old_concept = self.concepts.pop(cid)
            new_id_1, new_id_2 = self.next_concept_id, self.next_concept_id + 1
            self.next_concept_id += 2
            self.concepts[new_id_1] = Concept(new_id_1, old_concept.pos + old_concept.vel * 0.1)
            self.concepts[new_id_2] = Concept(new_id_2, old_concept.pos - old_concept.vel * 0.1)
            print(f"FISSION: Concept {cid} split into {new_id_1} and {new_id_2}")

        # Fusion
        fused_ids = set()
        concepts_to_fuse = list(self.concepts.items())
        for i in range(len(concepts_to_fuse)):
            for j in range(i + 1, len(concepts_to_fuse)):
                cid1, c1 = concepts_to_fuse[i]
                cid2, c2 = concepts_to_fuse[j]
                if cid1 in fused_ids or cid2 in fused_ids: continue
                if np.linalg.norm(c1.pos - c2.pos) < TAU_FUSION:
                    new_id = self.next_concept_id
                    self.next_concept_id += 1
                    self.concepts[new_id] = Concept(new_id, (c1.pos + c2.pos) / 2)
                    if cid1 in self.concepts: self.concepts.pop(cid1)
                    if cid2 in self.concepts: self.concepts.pop(cid2)
                    fused_ids.add(cid1)
                    fused_ids.add(cid2)
                    print(f"FUSION: Concepts {cid1} and {cid2} merged into {new_id}")
                    concepts_to_fuse = list(self.concepts.items())
                    return

    def step(self):
        """Performs a single step of the simulation."""
        self._apply_graph_forces()
        self._apply_attractor_forces()
        self._update_positions()
        self._handle_dynamic_events()

# --- Visualization Setup ---
sim = Simulation()
fig, ax = plt.subplots(figsize=(10, 10))
force_quiver = None # Initialize quiver to None

def setup_plot():
    """Sets up the plot aesthetics for the initial frame."""
    ax.clear()
    ax.set_xlim(-8, 8)
    ax.set_ylim(-8, 8)
    ax.set_aspect('equal', adjustable='box')
    ax.set_title("Continuum Architecture Simulation (GMM - Fixed)")
    ax.grid(True, linestyle='--', alpha=0.6)

    x = np.linspace(-8, 8, 100)
    y = np.linspace(-8, 8, 100)
    X, Y = np.meshgrid(x, y)
    XX = np.array([X.ravel(), Y.ravel()]).T

    colors = ['Blues', 'Greens', 'Reds', 'Oranges', 'Purples']
    for i, attractor in enumerate(sim.attractors):
        Z = np.exp(attractor.gmm.score_samples(XX))
        Z = Z.reshape(X.shape)
        ax.contourf(X, Y, Z, levels=7, cmap=colors[i % len(colors)], alpha=0.4)

concept_nodes = ax.scatter([], [], s=100, c='black', zorder=5)
step_text = ax.text(0.02, 0.95, '', transform=ax.transAxes)

def update(frame):
    """The main animation function, called for each frame."""
    global force_quiver
    sim.step()

    positions = np.array([c.pos for c in sim.concepts.values()]) if sim.concepts else np.empty((0, D_CONCEPT))
    forces = np.array([c.force for c in sim.concepts.values()]) if sim.concepts else np.empty((0, D_CONCEPT))

    concept_nodes.set_offsets(positions)

    if force_quiver:
        force_quiver.remove()

    if positions.size > 0:
        force_quiver = ax.quiver(positions[:, 0], positions[:, 1],
                                 forces[:, 0], forces[:, 1],
                                 color='r', alpha=0.7, zorder=4, width=0.005,
                                 scale=1.0, scale_units='xy')
    else:
        force_quiver = None

    step_text.set_text(f"Step: {frame+1}/{N_STEPS}\nConcepts: {len(sim.concepts)}")
    return [concept_nodes, force_quiver, step_text] if force_quiver else [concept_nodes, step_text]

# --- Run the Animation ---
setup_plot()
ani = FuncAnimation(fig, update, frames=N_STEPS, blit=False, interval=50, repeat=False)

try:
    print("Saving animation to continuum_simulation_gmm_fixed.gif... This may take a moment.")
    ani.save('continuum_simulation_gmm_fixed.gif', writer='imagemagick', fps=20)
    print("Animation saved successfully.")
except Exception as e:
    print(f"\nCould not save animation. Error: {e}")
    print("Please ensure you have ImageMagick installed and configured for Matplotlib.")
    print("Displaying animation inline instead.")
    plt.show()

