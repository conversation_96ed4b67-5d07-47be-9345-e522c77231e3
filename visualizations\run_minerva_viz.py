"""
Minerva 0.17 Visualization Suite
Demonstrates the complete Graph-Manifold architecture with interactive controls
"""

import sys
import matplotlib
matplotlib.use('TkAgg')  # Ensure we're using an interactive backend
import matplotlib.pyplot as plt
from minerva_viz import MinervaVisualizer
from minerva_components import create_architecture_diagram, create_energy_landscape

def run_all_visualizations():
    """Run all visualizations in separate windows"""
    print("\n📊 Starting Interactive Graph-Manifold Simulation...")
    print("Controls:")
    print("- Checkboxes: Toggle component visibility")
    print("- Play/Pause: Start/stop animation")
    print("- Step: Single simulation step")
    print("- Sliders: Adjust temperature and energy weights")
    
    # Create the interactive simulation
    visualizer = MinervaVisualizer(num_nodes=8)
    
    print("\n🏗️ Generating Architecture Diagram...")
    fig_arch = create_architecture_diagram()
    
    print("\n🌄 Generating Energy Landscape Analysis...")
    fig_energy = create_energy_landscape()
    
    print("\n✅ All visualizations created. Close windows to exit.")
    
    # Start the interactive event loop
    visualizer.run()

def run_interactive_only():
    """Run just the interactive simulation"""
    print("\n📊 Starting Interactive Graph-Manifold Simulation...")
    print("Controls:")
    print("- Checkboxes: Toggle component visibility")
    print("- Play/Pause: Start/stop animation")
    print("- Step: Single simulation step")
    print("- Sliders: Adjust temperature and energy weights")
    
    visualizer = MinervaVisualizer(num_nodes=8)
    visualizer.run()

def main():
    # Check if we have command line arguments
    if len(sys.argv) > 1:
        choice = sys.argv[1]
        if choice == "all":
            run_all_visualizations()
        elif choice == "interactive":
            run_interactive_only()
        elif choice == "architecture":
            print("\n🏗️ Generating Architecture Diagram...")
            fig_arch = create_architecture_diagram()
            plt.show()
        elif choice == "energy":
            print("\n🌄 Generating Energy Landscape Analysis...")
            fig_energy = create_energy_landscape()
            plt.show()
        else:
            print(f"Unknown option: {choice}")
            print("Usage: python run_minerva_viz.py [all|interactive|architecture|energy]")
    else:
        # Default to running all visualizations
        print("Minerva 0.17 Graph-Manifold Visualization Suite")
        print("=" * 50)
        run_all_visualizations()

if __name__ == "__main__":
    main()
