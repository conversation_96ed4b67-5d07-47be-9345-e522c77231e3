# Continuum: A Graph–Manifold Framework for Dynamic Thought Simulation

Version 0.17 – 2025-07-31

## Change Log

| Version | Date | Changes |
| --- | --- | --- |
| v0.17 | 2025-07-31 | Seventeenth Major Revision: Introduces the Graph–Manifold formalism. The Planner now seeds a coupled state consisting of factorized node fields and typed edge weights that define local geometric structure (metrics and potentials). Node fields and edge weights co-evolve under a separable energy via alternating updates (continuous for node states, proximal for edges). Structural and Semantic attractor fields are Gaussian Mixture Domain families defined in compact charts. Adds a principled extraction pipeline from the converged Graph–Manifold to interpreter inputs (ConceptRecords and a discourse scaffold). |
| v0.16 | 2025-07-31 | Sixteenth Major Revision: Re-architected Attractor Fields as Probabilistic Domains (GMMs) with analytical gradients for efficient force computation. Added maintenance mechanisms (instantiate/fuse/dormancy) for attractor fields. |
| v0.15 | 2025-07-31 | Introduced Attractor Field Maintenance and Growth; long-term self-regulation mechanisms. |

## Abstract

Continuum v0.17 upgrades the reasoning substrate from a “graph embedded in a manifold” to a Graph–Manifold: a unified object where node states inhabit a shared latent space and typed edges induce local geometric structure via relation-specific metrics and potentials. Two families of probabilistic attractor domains—Semantic and Structural—act as Gaussian Mixture Models (GMMs) in compact charts, exerting responsibility-weighted forces. Valence (role/modality/time) modulates local metrics, potential strengths, and domain temperatures. Refinement proceeds by alternating optimization over a separable energy: node states update via continuous gradient flow with field-wise normalization; edge weights update via proximal steps that enforce hard structural constraints. Energy-based scheduling governs fission/fusion and edge edits. After convergence, a canonicalization and extraction stage produces ConceptRecords, a pruned typed graph, and a discourse scaffold that condition a probabilistic interpreter. This co-evolving design improves coherence, stability, and interpretability while preserving modular ablations and safety constraints.

## 1. The Continuum Architecture

The Continuum model operates in three phases: Graph–Manifold Seeding (Planner), Simulation, and Interpretation.

### 1.1 Phase 1: Graph–Manifold Seeding (Planner)

Goal: Seed an initial Graph–Manifold state, not a fixed graph to be embedded post hoc.

- Planner Module: Processes the prompt using a deterministic, self-contained procedure (e.g., a bi-directional GRU or Transformer encoder).
- Factorized Node Fields Initialization:
  - V_sem(k) ∈ R^D: semantic content
  - V_role(k) ∈ R^{d_r}: argumentative/discourse roles
  - V_mod(k) ∈ R^{d_m}: modality/negation/polarity
  - V_time(k) ∈ R^{d_t}: tense/aspect/temporal anchor
  - V_uncert(k) ∈ R^2: heteroscedastic log-variances
  - V_type(k) ∈ R^{d_ty}: type/sense logits
  - Optional: V_link(k): KB/entity/link features
- Typed Subspaces (Charts):
  - P_sem: R^D → R^{d′_sem} for Semantic domains (d′_sem ≈ 64–128)
  - P_τ: R^{D_all} → R^{d′_τ} per relation type τ (D_all is a selected concatenation of fields)
  - P_val: R^{D_all} → R^{d′_val} for valence modulation
- Typed Edges as Geometry:
  - Relational Kernels propose typed edge confidences. The Planner initializes typed edge weights A_τ that parameterize local metrics and potentials in relation-specific charts; these are part of the manifold’s definition.
- Valence Modulation (initialization):
  - Valence fields modulate metric axes (stiffness) and potential strengths via P_val, and set initial temperatures for domain responsibilities based on V_uncert/V_mod.
- Initialization Energy (brief joint optimization):
  - E_init(V̄, A) = α E_sem_init(V̄) + β E_edges_init(V̄, A) + γ E_rules(A)
  - Run a few alternating steps: V̄ ← V̄ − η_V ∇_{V̄}E_init; A ← Prox_rules(A − η_A ∇_A E_init)
  - This yields the initial Graph–Manifold state to enter the Simulation phase.

### 1.2 Phase 2: The Graph–Manifold Simulation

The simulation operates on a Graph–Manifold: node states live in a shared latent space; typed edges induce local geometric structure (metrics and potentials). Structural and Semantic attractors are GMM domain families in compact charts. Node fields and edge weights co-evolve under a separable energy via alternating updates: continuous gradient steps for node states and proximal, constraint-aware steps for edges. Cached gating and sparse retrieval ensure tractability.

- Inductive Cache (warm start):
  - Hybrid cache with versioning by parameter hashes:
    - Manifold cache: keyed by (P_sem(V̄_0), top-k domain IDs)
    - Graph cache: keyed by WL/GNN fingerprint of A_0
  - Warm start retrieves both and runs K short alternations to reconcile to current parameters.
- Cached Gating:
  - Gating scores for domain families are updated every N steps and cached in between; EMA smoothing and sticky top-k reduce oscillation.

### 1.3 Phase 3: Interpretation (Probabilistic)

The interpreter reads the converged Graph–Manifold via an explicit extraction interface:
- Snapshot (V̄*, A*), responsibilities, and chart summaries.
- Canonicalize nodes (merge/prune), select stable typed edges with hysteresis to form G_out.
- Build ConceptRecords per node (semantic anchors, roles, modality/negation, time, uncertainty, types/links, local chart summaries, incident edges).
- Construct a discourse scaffold (macro from causal/precedence; micro from support/elaboration; insert contradictions/concessions).
- Decoder consumes ConceptRecords through semantic/structural/valence adapters; optional symbolic IR enables semi-deterministic decoding.

## 3. Formalisms and Mathematical Details

### 3.1 Planner and Graph–Manifold Initialization

3.1.1 Factorized Node Fields
- Node k: V̄(k) = {V_sem, V_role, V_mod, V_time, V_uncert, V_type, V_link}

3.1.2 Typed Charts (Subspaces)
- P_sem: R^D → R^{d′_sem} (input: V_sem only)
- P_τ: R^{D_all} → R^{d′_τ} for each relation type τ (input: concatenation of selected fields; default [V_sem, V_role, V_mod, V_time])
- P_val: R^{D_all} → R^{d′_val} (input: valence modulation features; default [V_role, V_mod, V_uncert])
- P_str: R^{D_all} → R^{d′_str} for Structural domain families (input: default [V_sem, V_role, V_time, optional V_link]; typically excludes V_mod unless modality should affect structural priors)

3.1.3 Edge-Typed Local Metrics and Potentials
- For relation type τ:
  - Positive-definite metric in chart P_τ (global per type):
    - M_τ = L_τ L_τ^T + εI (low-rank + diagonal; ε > 0; PD via Cholesky parameterization with eigenvalue floors and caps)
  - Typed potentials between nodes i, j, scaled by A_τ(i,j):
    - Attraction (quadratic): U^attr_τ(i,j) = w_τ · ||P_τ(V̄(i)) − P_τ(V̄(j))||^2_{M_τ}
    - Repulsion (bounded decreasing): U^rep_τ(i,j) = w_τ · exp(−||P_τ(V̄(i)) − P_τ(V̄(j))||^2_{M_τ} / σ_τ^2)
      · Default σ_τ set to median typed distance in mini-batch (EMA-updated), clipped to [σ_min, σ_max]
    - Directional margin (for precedence/causal):
      U^dir_τ(i,j) = w_τ · max(0, ⟨u_τ, P_τ(V̄(i))⟩ − ⟨u_τ, P_τ(V̄(j))⟩ + δ_τ), with u_τ learned per τ and ||u_τ||_2=1
- Metrics vs potentials coupling (clarification):
  - Default (recommended): A_τ gates potentials only; M_τ is global per τ and used in distances.
  - Optional (advanced): Composite local metric at node i: M_eff(i) = Σ_τ Σ_j ŵ_τ(i,j) · M_τ, where ŵ_τ(i,j) ∝ A_τ(i,j) · g_val(i,j,τ); normalize weights to sum ≤ 1, ensure PD by convex combination, and cap condition numbers. Use only if needed; otherwise prefer potentials-only gating.
- These typed metrics and potentials are part of the manifold’s definition. The edge layer determines preferred directions; optionally it can contribute to composite local metrics if enabled.

3.1.4 Valence Modulation
- Valence modulates geometry and potentials:
  - Metric modulation (optional when composite metrics enabled): M_τ’(i,j) = f_val(M_τ; P_val(V_role(i), V_mod(i), V_role(j), V_mod(j))) via axis-wise softplus scalings, constrained to maintain PD and bounded condition number.
  - Potential scaling: w_τ’(i,j) = w_τ · σ(a_τ^T P_val(…))
  - Domain temperatures: implement as covariance scaling Σ' = Σ · t, where t = clip(softplus(a_T^T P_val(…)), t_min, t_max); t is EMA-smoothed per node to avoid oscillations.
  - V_uncert is heteroscedastic log-variance; map to temperature by t_uncert = exp(κ · V_uncert), then combine with valence temperature multiplicatively with clipping.

3.1.5 Initialization Energy
- E_init(V̄, A) = α E_sem_init(V̄) + β E_edges_init(V̄, A) + γ E_rules(A)
- E_sem_init: semantic fit to seed prototypes in P_sem
- E_edges_init: typed potentials with M_τ, initial A_τ
- E_rules: sparsity/degree/type priors; enforced via proximal steps
- Run alternating updates (few steps) to produce the starting Graph–Manifold state.

### 3.2 Simulation Initialization with Inductive Cache

- The initial state (V̄(t=0), A(t=0)) may be loaded via hybrid cache warm start and reconciled with K short alternations to current parameters.

### 3.3 Graph–Manifold Energy and Equations of Motion

We define a separable energy over node fields V̄ and typed edges A. All forces on node states are negative gradients of this energy. Edge updates are proximal steps enforcing hard constraints. Edges and geometry are inseparable: edge types define local charts and metrics; Semantic and Structural GMM families act as fields in their charts.

3.3.1 Composite Energy
- E(V̄, A) = α E_sem(V̄) + β E_edges(V̄, A) + γ E_struct_domains(V̄) + ρ E_rules(A) + ζ E_align(V̄, A)

3.3.2 Semantic Domains (GMM Family, P_sem)
- E_sem(V̄) = −∑_k log ∑_{j∈Semantic} ∑_{i=1}^M π_{j,i} N(P_sem(V_sem(k)) | μ_{j,i}, Σ'_{j,i})
  where Σ'_{j,i} = Σ_{j,i} · t_k with t_k from 3.1.4 (covariance scaling).
- Responsibility-weighted force on node k:
  - F_sem(k) = −∂E_sem/∂V̄(k) = ∑_j g_sem(k,j) ∑_i γ_{j,i}(k) · (−P_{j,i}(P_sem(V_sem(k)) − μ_{j,i})) back-projected via P_sem
- Numerics: Cholesky parameterization with eigenvalue floors and caps; sparse top-k gates with EMA and sticky selection; temperatures t_k clipped to [t_min, t_max]; use log-sum-exp stabilization; clip mixture logits for π.

3.3.3 Structural Domains (GMM Family, P_str)
- Universal structural bank T_q in structural charts:
  - E_struct_domains(V̄) = −∑_k log ∑_{q∈Structural} ∑_{i=1}^M π_{q,i} N(P_str([V_sem(k), V_role(k), V_time(k), V_link(k)]) | μ_{q,i}, Σ'_{q,i})
  where Σ'_{q,i} = Σ_{q,i} · t_k (temperature from 3.1.4).
- Independent gating g_str; sparse; temperature modulated by V_uncert; top-k with EMA and stickiness.

3.3.4 Typed Edge Potentials (P_τ with M_τ’)
- E_edges(V̄, A) = ∑_{τ} ∑_{i,j} A_τ(i,j) · U_τ(i,j; P_τ(V̄), M_τ’)
- U_τ selects attraction/repulsion/directional forms by τ
- M_τ’ and w_τ’ are valence-modulated via P_val

3.3.5 Alignment and Rules
- E_align(V̄, A): bounded interface encouraging distance–edge consistency and domain–graph agreement.
  - Define distance-implied edge likelihood for each τ:
    p̂_τ(i,j) = σ(b_τ − ||P_τ(V̄(i))−P_τ(V̄(j))||_{M_τ}), with b_τ learned bias.
    Then E_dist-align = BCE(p̂_τ(i,j), σ(A_τ(i,j))) aggregated over sampled pairs.
  - Define graph-induced pseudo-responsibilities ĥ_resp for domains:
    For node k and semantic family, ĥ_resp_k ∝ Σ_τ Σ_j A_τ(k,j) · Map_τ(resp_j), where Map_τ projects neighbor responsibilities into the semantic index space; normalize to a distribution. Then
    E_resp-align = KL(ĥ_resp_k || resp_k) aggregated over nodes.
  - E_align = ζ_1 · E_dist-align + ζ_2 · E_resp-align with ζ schedule ramped from 0 after pretrain.
- E_rules(A): structural constraints enforced via proximal operators:
  - Sparsity budgets: per-row top-k keep; others zero (with hysteresis).
  - Degree limits: project row/column ℓ1 norms to caps via fast projection or Dykstra.
  - Type validity: mask invalid entries pre- and post-prox.
  - Optional acyclicity (designated subgraphs): NOTEARS-style penalty R_dag(A) = τ · trace(exp(A∘A)) used as a smooth penalty rather than strict proximal unless subgraphs are small; apply cooldown.

3.3.6 Equations of Motion (Alternating Updates)
- Node update (continuous):
  - V̄_{t+1} = FieldNorm(V̄_t − η_V,t · clip(∂E/∂V̄_t, −g_clip_V, g_clip_V))
  - FieldNorm (explicit): for each field f ∈ {sem, role, mod, time}:
    V_f ← (V_f − mean_batch(V_f)) / max(ε, std_batch(V_f)) · s_f, with learned s_f (or fixed), EMA-updated for stability. RMSNorm-style per-sample variant is permissible.
  - Gating caches refresh every N steps with EMA; sticky top-k per family/chart.
- Edge update (proximal per type τ):
  - A_{τ,t+1} = Prox_{λ, rules}(A_{τ,t} − η_A,t · clip(∂E/∂A_{τ,t}, −g_clip_A, g_clip_A))
  - Prox enforces sparsity budgets (top-k per row), degree caps via ℓ1 projection, type masks, and hysteresis/cooldown to prevent thrash.
- Scheduling:
  - Alternate updates with A:V̄ ≈ 1:2–1:5; trigger edge steps when ΔE_align or rule violations exceed thresholds, or on tension spikes; otherwise skip edge step to save cycles.

3.3.7 Dynamic Manifold Operations (Fission/Fusion)
- Tension: T = Trace(Cov_k(−∂E/∂V̄_k)) evaluated in P_sem; maintain EMA of T for stability.
- Fission: trigger if T > τ_tension (calibrated as p95 from burn-in) for S=3–5 consecutive steps. Split along top eigen-direction(s) of local force covariance in P_sem. Initialize V_new1/V_new2 with small, role/time-aware perturbations. Edge reallocation: split A(:,k)/A(k,:) by nearest prototypes in P_sem or by responsibility proportions; then apply Prox_rules and cooldown (10–20 steps) before allowing further topology edits.
- Fusion: when typed Mahalanobis distances between nodes are below δ_merge across relevant τ and semantic/structural responsibilities align (Jaccard ≥ 0.6), merge via uncertainty-weighted averaging in charts; merge adjacency with normalization; re-apply Prox_rules; apply hysteresis to avoid churn.

3.3.8 Scheduling and Convergence
- Trigger edge updates and topology checks when ΔE_align, rule violations, or tension spikes exceed thresholds; otherwise prioritize node updates to reduce oscillations.
- Convergence when:
  - ||V̄_{t+1} − V̄_t||_2 < ε_V for S steps
  - ||A_{t+1} − A_t||_F < ε_A for S steps
  - Stable active domain/edge sets for S steps
- Log ΔE, per-term gradient norms, active set stability, and temperature statistics for diagnostics.

### 3.4 Interpreter-Oriented Concept Extraction (New)

Goal: Convert the converged Graph–Manifold (V̄*, A*) into a stable set of concepts and a typed graph for language generation.

4.1 Snapshot and Canonicalization
- Snapshot (V̄*, A*), responsibilities (top-k per node), chart summaries, and gates
- Canonicalize nodes:
  - Merge twins when typed Mahalanobis distance in P_sem is below δ_merge (default 1.5) across ≥80% of active semantic components and responsibility overlap Jaccard ≥ 0.6, with hysteresis to avoid churn.
  - Prune low-salience nodes unless anchored by V_link
- Select stable edges with proximal thresholds and hysteresis; for directional types, order by u_τ projections; for symmetric, prefer high joint-consistency

4.2 ConceptRecords
- For each node k, construct:
  - Semantic anchors: top-k semantic components (IDs, embeddings, γ), plus MAP component and confidence
  - Role/modality/time: calibrated distributions and flags from V_role*/V_mod*/V_time*; temperatures from V_uncert*
  - Types/links: V_type*, V_link*
  - Local chart summaries: principal stiffness directions from M_τ’ around k
  - Incident edges: typed edges with weights and order indices
  - Uncertainty: V_uncert*, responsibility entropy

4.3 Discourse Scaffold
- Macro order: topological sort of causal/precedence subgraph using u_τ projections
- Micro order: SUPPORT/ELABORATE trees; insert CONTRADICT/CONCESSION as adversatives or caveats
- Optional symbolic IR: clause skeleton with slot constraints (tense/polarity/connectives)

4.4 Interpreter Conditioning
- Semantic adapter: P_sem(V_sem*) and top-k anchors guide lexical choice and paraphrasing
- Structural adapter: typed edges and chart features control discourse markers, nesting, and ordering
- Valence adapter: role/modality/uncertainty controls polarity, hedges, tense/aspect
- Optional constrained decoding via IR; otherwise, soft guidance through adapters
- Referential consistency via V_link* and COREF edges

4.5 Metrics
- Fidelity: reconstruction error of ConceptRecords from (V̄*, A*)
- Stability: robustness of G_out under small perturbations
- Interpretability: human evaluations of labels/relations
- Downstream: BLEU/ROUGE, factuality, discourse coherence

## 4. Training Paradigm and Self-Correction

### 4.1 Attractor Training (GMMs)

- Semantic and Structural domain families trained in phases:
  - Phase 1: Distillation seeding from teacher models and KBs
  - Phase 2: Refinement on structured data
  - Phase 3: In-situ self-correction

### 4.2 Graph–Manifold Alternating Training

- Two optimizers and schedules:
  - Node optimizer (V̄): gradient-based with FieldNorm, per-field step sizes
  - Edge optimizer (A): proximal with hard constraints, cooldown, hysteresis
- Curriculum:
  - Pretrain E_sem and E_edges separately (ζ=0)
  - Enable E_align with small ζ; alternate updates at ~3:1 node:edge
  - Activate fission/fusion and dynamic edge edits with cooldown
  - Fine-tune interpreter on ConceptRecords and scaffold

### 4.3 Efficiency and Stability Losses

- Simulation efficiency:
  - L_efficiency = w_steps·T_stable + w_tension·∑_{t≤T_stable} ∑_k Tension(V̄_k^t)
- Regularization:
  - Responsibility sparsity, component usage regularizers
  - Covariance conditioning penalties; entropy on gates to avoid collapse

### 4.4 Attractor Field Maintenance and Growth (GMM Families)

- Instantiation via semantic stress clustering (in charts)
- Fusion via gradient similarity (component-level merges across GMMs)
- Dormancy via relevance decay (EMA of gating scores)
- All adapted to chart-specific parameterizations; with logging and safeguards

## 5. Component Specifications and Hyperparameters

Table: Module Architectures
- Planner: Bi-GRU or Transformer encoder; relational kernels; multi-head initialization of fields
- Graph–Manifold:
  - Charts: learned projections P_sem, P_τ, P_val, P_str
  - Typed Metrics: M_τ low-rank+diag, Cholesky PD with eigenvalue floors and caps
  - Domain Families: Semantic/Structural GMM banks with temperature scaling and EMA-gated top-k
  - Alternating Optimizers: gradient for V̄ (with FieldNorm and gradient clipping), proximal for A (with clipping, hysteresis, cooldown)
  - Maintenance: fission/fusion logic with eigen-direction splits, calibrated thresholds, and constrained edge reallocation
- Interpreter: 2-layer Transformer decoder with semantic/structural/valence adapters; optional IR-constrained decoding

Key Hyperparameters (additions)
- d′_sem, d′_τ, d′_val, d′_str: 64–128
- α, β, γ, ρ, ζ_1, ζ_2: energy term weights (runtime toggles); ζ ramp-up schedule
- chart_top_k_domains (or per-family: top_k_semantic_domains, top_k_structural_domains): 8–16
- chart_gate_ema: 0.9–0.98; stickiness window
- alternation_ratio (edge:node): ~1:3 (tunable); adaptive edge triggers via ΔE_align/rule violations/tension
- edge_cooldown_steps: 10–20
- prox_sparsity_budget per node/type; degree caps per row/column
- η_V schedule: 0.1 → 0.001; per-field step sizes; g_clip_V (e.g., 1.0–5.0)
- η_A schedule with proximal thresholds; g_clip_A (e.g., 1.0–5.0)
- τ_tension (calibrated p95), δ_merge≈1.5, τ_instability; hysteresis windows
- Temperature bounds t_min, t_max; σ_τ bounds [σ_min, σ_max]

## 6. Evaluation and Ablations

- Simulation Acceleration Factor (warm vs. cold starts)
- Stability/Convergence distributions; active set stability metrics
- Text quality: BLEU/ROUGE; discourse coherence; factuality/calibration
- Attractor Field Dynamics (fusion/dormancy/instantiation rates; complexity evolution)
- Interpretability: alignment of typed edges and semantic anchors
- Proposed Ablations:
  - No E_align (interface off)
  - No Structural domains
  - No Valence modulation
  - Fixed A (edges frozen)
  - Single-Vector Attractors (baseline)
  - Deterministic interpreter (constrained decoding only)

## 7. Conclusion

The Graph–Manifold recasts the Continuum simulation as a unified, co-evolving geometry–structure system. Relation-typed metrics and potentials make edges geometry-defining, while Semantic/Structural GMM family fields shape content and rules in compact charts. Valence modulates metrics, potentials, and domain temperatures to preserve argumentative and modal nuance. Alternating optimization with proximal control yields a principled and operable dynamics with unified scheduling, stability, and interpretability. The extraction pipeline produces discrete ConceptRecords and a typed graph that faithfully reflect the manifold’s converged state, enabling higher-fidelity, controllable interpretation.
