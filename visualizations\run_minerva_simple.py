"""
Direct launcher for Minerva visualization - no prompts
"""

import matplotlib
matplotlib.use('TkAgg')  # Ensure interactive backend
from minerva_viz import MinervaVisualizer

# Create and run the visualization directly
print("Starting Minerva 0.17 Graph-Manifold Visualization...")
print("\nControls:")
print("- Checkboxes on the right: Toggle different visualization layers")
print("- Play button: Start/stop the simulation animation")
print("- Step button: Advance simulation by one step")
print("- Temperature slider: Adjust domain field strength")
print("- α, β, γ sliders: Adjust energy term weights")
print("\nClose the window to exit.")

visualizer = MinervaVisualizer(num_nodes=8)
visualizer.run()
