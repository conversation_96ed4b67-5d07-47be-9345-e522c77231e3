# Minerva 0.17 Visualization Suite

This visualization suite provides interactive mathematical visualizations of the Minerva 0.17 Graph-Manifold architecture.

## Features

### 1. Interactive Graph-Manifold Simulation (`minerva_viz.py`)
- **Real-time simulation** of node dynamics and edge evolution
- **Toggleable overlays** for different components:
  - Semantic Fields (GMM domains)
  - Structural Fields 
  - Edge Potentials (typed relations)
  - Valence Modulation
  - Node Fields
  - Energy Landscape
  - Chart Projections
- **Interactive controls**:
  - Play/Pause animation
  - Single-step simulation
  - Temperature adjustment
  - Energy weight sliders (α, β, γ)
- **Multiple visualization panels**:
  - Main graph-manifold state
  - 3D energy landscape
  - Chart projections (P_sem, P_role, P_val)
  - Energy evolution history
  - Node field details
  - Mathematical formulas

### 2. Architecture Diagram (`minerva_components.py`)
- Complete three-phase architecture visualization:
  - **Phase 1**: Graph-Manifold Seeding (Planner)
  - **Phase 2**: Simulation with alternating optimization
  - **Phase 3**: Interpretation and extraction
- Shows data flow and component relationships

### 3. Energy Landscape Analysis
- 3D energy surface visualization
- Optimization trajectory tracking
- Force field (gradient) visualization
- Energy evolution plots

## Installation

```bash
# Required dependencies
pip install numpy matplotlib scipy networkx
```

## Usage

### Quick Start
```bash
python run_minerva_viz.py
```

### Individual Components

```python
# Interactive simulation
from minerva_viz import MinervaVisualizer
viz = MinervaVisualizer(num_nodes=12)
viz.run()

# Architecture diagram
from minerva_components import create_architecture_diagram
fig = create_architecture_diagram()
plt.show()

# Energy landscape
from minerva_components import create_energy_landscape
fig = create_energy_landscape()
plt.show()
```

## Mathematical Components Visualized

### Energy Terms
- **E_sem**: Semantic domain energy (GMM log-likelihood)
- **E_edges**: Typed edge potentials with relation-specific metrics
- **E_struct**: Structural domain constraints
- **E_rules**: Sparsity and degree constraints
- **E_align**: Distance-edge consistency

### Update Dynamics
- **Node updates**: Gradient flow with FieldNorm
- **Edge updates**: Proximal operators with hard constraints
- **Fission/Fusion**: Topology changes based on tension

### Key Formulas
- Typed metric: M_τ = L_τL_τᵀ + εI
- Attraction potential: U_τ^attr(i,j) = w_τ·||P_τ(V̄(i)) - P_τ(V̄(j))||²_M_τ
- Node update: V̄_{t+1} = FieldNorm(V̄_t - η_V·∇E)
- Edge update: A_{τ,t+1} = Prox_{λ,rules}(A_{τ,t} - η_A·∇E)

## Customization

### Adjusting Simulation Parameters
```python
viz = MinervaVisualizer(
    num_nodes=16,        # Number of nodes
    seed=42             # Random seed for reproducibility
)
```

### Modifying Domain Families
Edit `_initialize_semantic_domains()` and `_initialize_structural_domains()` in `minerva_viz.py` to change:
- Number of domains
- Components per domain
- Domain positions and covariances
- Domain colors

### Adding New Edge Types
Add to the `edge_types` list in `_initialize_edges()` and define visualization style in `edge_styles` dictionary.

## Keyboard Shortcuts (when matplotlib window is active)
- `Space`: Toggle play/pause
- `→`: Single step forward
- `r`: Reset simulation
- `s`: Save current frame

## Understanding the Visualization

### Node Representation
- **Circle**: Node position in manifold
- **Number**: Node ID
- **Green arrow**: Role indicator (argument direction)
- **Dotted red circle**: Uncertainty visualization

### Edge Representation
- **Color**: Edge type (green=SUPPORT, blue=ELABORATE, red=CONTRADICT, etc.)
- **Thickness**: Edge weight (A_τ value)
- **Style**: Solid/dashed based on relation type

### Domain Visualization
- **Contours**: GMM component boundaries
- **Colors**: Different semantic/structural families
- **Temperature**: Affects contour spread (covariance scaling)

## Troubleshooting

### Performance Issues
- Reduce `num_nodes` for faster simulation
- Increase animation interval in `FuncAnimation`
- Disable energy landscape computation (`show_energy_landscape = False`)

### Visual Clarity
- Adjust domain transparency with `alpha` parameter
- Modify edge weight threshold for cleaner visualization
- Use color schemes suitable for colorblind users

## Mathematical Accuracy Note

This visualization simplifies some aspects for clarity:
- 2D projection of high-dimensional spaces
- Approximate gradient computations
- Simplified proximal operators

For exact mathematical definitions, refer to `minerva0.17_graphManifold.md`.
