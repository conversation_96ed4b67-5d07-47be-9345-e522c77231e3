import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import FancyBboxPatch, Circle, FancyArrowPatch
from matplotlib.widgets import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>
from matplotlib.animation import FuncAnimation
import matplotlib.gridspec as gridspec
from mpl_toolkits.mplot3d import Axes3D
import networkx as nx
from scipy.stats import multivariate_normal
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
import colorsys

@dataclass
class NodeState:
    """Represents a node in the Graph-Manifold"""
    id: int
    V_sem: np.ndarray  # Semantic embedding
    V_role: np.ndarray  # Role features
    V_mod: np.ndarray   # Modality features
    V_time: np.ndarray  # Temporal features
    V_uncert: np.ndarray  # Uncertainty
    V_type: np.ndarray  # Type logits
    position: np.ndarray  # 2D/3D position for viz

@dataclass
class DomainComponent:
    """Gaussian component in a domain family"""
    mean: np.ndarray
    cov: np.ndarray
    weight: float
    color: str

class MinervaVisualizer:
    def __init__(self, num_nodes=12, seed=42):
        np.random.seed(seed)
        self.num_nodes = num_nodes
        self.nodes = self._initialize_nodes()
        self.edges = self._initialize_edges()
        self.semantic_domains = self._initialize_semantic_domains()
        self.structural_domains = self._initialize_structural_domains()
        
        # Visualization state
        self.show_semantic_field = True
        self.show_structural_field = True
        self.show_edge_potentials = True
        self.show_valence_modulation = True
        self.show_node_fields = True
        self.show_energy_landscape = True
        self.show_charts = True
        self.animation_running = False
        self.time_step = 0
        
        # Energy tracking
        self.energy_history = []
        
        self._setup_figure()
        self._setup_controls()
        self._update_visualization()
        
    def _initialize_nodes(self):
        """Initialize node states in the Graph-Manifold"""
        nodes = []
        # Create nodes in a circle initially
        angles = np.linspace(0, 2*np.pi, self.num_nodes, endpoint=False)
        for i in range(self.num_nodes):
            node = NodeState(
                id=i,
                V_sem=np.random.randn(128),  # D=128
                V_role=np.random.randn(32),   # d_r=32
                V_mod=np.random.randn(16),    # d_m=16
                V_time=np.random.randn(16),   # d_t=16
                V_uncert=np.random.randn(2),  # heteroscedastic
                V_type=np.random.randn(24),   # d_ty=24
                position=np.array([np.cos(angles[i])*3, np.sin(angles[i])*3])
            )
            nodes.append(node)
        return nodes
    
    def _initialize_edges(self):
        """Initialize typed edge weights A_τ"""
        edge_types = ['SUPPORT', 'ELABORATE', 'CONTRADICT', 'PRECEDE', 'CAUSE']
        edges = {edge_type: np.random.rand(self.num_nodes, self.num_nodes) * 0.3 
                for edge_type in edge_types}
        
        # Make edges sparse
        for edge_type in edges:
            edges[edge_type][edges[edge_type] < 0.2] = 0
            np.fill_diagonal(edges[edge_type], 0)
        
        return edges
    
    def _initialize_semantic_domains(self):
        """Initialize Semantic GMM family"""
        domains = []
        colors = ['red', 'blue', 'green', 'purple', 'orange']
        for i in range(5):  # 5 semantic domains
            components = []
            for j in range(3):  # 3 components per domain
                comp = DomainComponent(
                    mean=np.random.randn(2) * 2,
                    cov=np.eye(2) * np.random.uniform(0.5, 1.5),
                    weight=np.random.uniform(0.2, 0.4),
                    color=colors[i]
                )
                components.append(comp)
            domains.append(components)
        return domains
    
    def _initialize_structural_domains(self):
        """Initialize Structural GMM family"""
        domains = []
        colors = ['cyan', 'magenta', 'yellow']
        for i in range(3):  # 3 structural patterns
            components = []
            for j in range(2):  # 2 components per domain
                comp = DomainComponent(
                    mean=np.random.randn(2) * 3,
                    cov=np.eye(2) * np.random.uniform(0.8, 2.0),
                    weight=0.5,
                    color=colors[i]
                )
                components.append(comp)
            domains.append(components)
        return domains
    
    def _setup_figure(self):
        """Setup the matplotlib figure with subplots"""
        self.fig = plt.figure(figsize=(20, 12))
        self.fig.suptitle('Minerva 0.17 Graph-Manifold Architecture', fontsize=16)
        
        # Create grid layout
        gs = gridspec.GridSpec(3, 4, figure=self.fig, hspace=0.3, wspace=0.3)
        
        # Main graph visualization
        self.ax_main = self.fig.add_subplot(gs[:2, :2])
        self.ax_main.set_title('Graph-Manifold State')
        self.ax_main.set_xlim(-6, 6)
        self.ax_main.set_ylim(-6, 6)
        self.ax_main.set_aspect('equal')
        
        # Energy landscape
        self.ax_energy = self.fig.add_subplot(gs[0, 2], projection='3d')
        self.ax_energy.set_title('Energy Landscape')
        
        # Chart projections
        self.ax_charts = self.fig.add_subplot(gs[1, 2])
        self.ax_charts.set_title('Chart Projections')
        
        # Energy history
        self.ax_history = self.fig.add_subplot(gs[2, :2])
        self.ax_history.set_title('Energy Evolution')
        self.ax_history.set_xlabel('Time Step')
        self.ax_history.set_ylabel('Energy')
        
        # Node field details
        self.ax_fields = self.fig.add_subplot(gs[2, 2])
        self.ax_fields.set_title('Node Fields')
        
        # Mathematical formulas
        self.ax_math = self.fig.add_subplot(gs[:, 3])
        self.ax_math.axis('off')
        self._draw_formulas()
    
    def _draw_formulas(self):
        """Draw key mathematical formulas"""
        formulas = [
            r'$E(\bar{V}, A) = \alpha E_{sem} + \beta E_{edges} + \gamma E_{struct} + \rho E_{rules} + \zeta E_{align}$',
            '',
            r'Semantic Force: $F_{sem}(k) = -\frac{\partial E_{sem}}{\partial \bar{V}(k)}$',
            r'$= \sum_j g_{sem}(k,j) \sum_i \gamma_{j,i}(k) \cdot (-P_{j,i}(P_{sem}(V_{sem}(k)) - \mu_{j,i}))$',
            '',
            r'Typed Metric: $M_\tau = L_\tau L_\tau^T + \epsilon I$',
            r'Attraction: $U^{attr}_\tau(i,j) = w_\tau \cdot ||P_\tau(\bar{V}(i)) - P_\tau(\bar{V}(j))||^2_{M_\tau}$',
            r'Repulsion: $U^{rep}_\tau(i,j) = w_\tau \cdot \exp(-||P_\tau(\bar{V}(i)) - P_\tau(\bar{V}(j))||^2_{M_\tau} / \sigma_\tau^2)$',
            '',
            r'Node Update: $\bar{V}_{t+1} = FieldNorm(\bar{V}_t - \eta_{V,t} \cdot clip(\frac{\partial E}{\partial \bar{V}_t}, -g_{clip}, g_{clip}))$',
            r'Edge Update: $A_{\tau,t+1} = Prox_{\lambda, rules}(A_{\tau,t} - \eta_{A,t} \cdot clip(\frac{\partial E}{\partial A_{\tau,t}}, -g_{clip}, g_{clip}))$',
            '',
            r'Tension: $T = Trace(Cov_k(-\frac{\partial E}{\partial \bar{V}_k}))$',
            r'Fission trigger: $T > \tau_{tension}$ for S steps'
        ]
        
        y_pos = 0.95
        for formula in formulas:
            if formula:
                self.ax_math.text(0.05, y_pos, formula, fontsize=10, 
                                 transform=self.ax_math.transAxes, 
                                 verticalalignment='top')
            y_pos -= 0.06
    
    def _setup_controls(self):
        """Setup interactive controls"""
        # Checkbox for component visibility
        ax_check = plt.axes([0.85, 0.4, 0.1, 0.2])
        labels = ['Semantic\nFields', 'Structural\nFields', 'Edge\nPotentials', 
                 'Valence\nModulation', 'Node\nFields', 'Energy\nLandscape', 'Charts']
        visibility = [True] * 7
        self.check_buttons = CheckButtons(ax_check, labels, visibility)
        self.check_buttons.on_clicked(self._toggle_visibility)
        
        # Animation controls
        ax_play = plt.axes([0.85, 0.3, 0.05, 0.04])
        self.btn_play = Button(ax_play, 'Play')
        self.btn_play.on_clicked(self._toggle_animation)
        
        ax_step = plt.axes([0.91, 0.3, 0.05, 0.04])
        self.btn_step = Button(ax_step, 'Step')
        self.btn_step.on_clicked(self._single_step)
        
        # Temperature slider
        ax_temp = plt.axes([0.85, 0.2, 0.1, 0.03])
        self.slider_temp = Slider(ax_temp, 'Temp', 0.1, 2.0, valinit=1.0)
        self.slider_temp.on_changed(self._update_temperature)
        
        # Energy weight sliders
        ax_alpha = plt.axes([0.85, 0.15, 0.1, 0.03])
        self.slider_alpha = Slider(ax_alpha, 'α', 0.0, 2.0, valinit=1.0)
        
        ax_beta = plt.axes([0.85, 0.1, 0.1, 0.03])
        self.slider_beta = Slider(ax_beta, 'β', 0.0, 2.0, valinit=1.0)
        
        ax_gamma = plt.axes([0.85, 0.05, 0.1, 0.03])
        self.slider_gamma = Slider(ax_gamma, 'γ', 0.0, 2.0, valinit=0.5)
    
    def _toggle_visibility(self, label):
        """Toggle component visibility"""
        if 'Semantic' in label:
            self.show_semantic_field = not self.show_semantic_field
        elif 'Structural' in label:
            self.show_structural_field = not self.show_structural_field
        elif 'Edge' in label:
            self.show_edge_potentials = not self.show_edge_potentials
        elif 'Valence' in label:
            self.show_valence_modulation = not self.show_valence_modulation
        elif 'Node' in label:
            self.show_node_fields = not self.show_node_fields
        elif 'Energy' in label:
            self.show_energy_landscape = not self.show_energy_landscape
        elif 'Charts' in label:
            self.show_charts = not self.show_charts
        self._update_visualization()
    
    def _toggle_animation(self, event):
        """Toggle simulation animation"""
        self.animation_running = not self.animation_running
        if self.animation_running:
            self.btn_play.label.set_text('Pause')
            self.animation = FuncAnimation(self.fig, self._animation_step, 
                                         interval=100, blit=False)
        else:
            self.btn_play.label.set_text('Play')
            if hasattr(self, 'animation'):
                self.animation.event_source.stop()
    
    def _single_step(self, event):
        """Execute single simulation step"""
        self._simulation_step()
        self._update_visualization()
    
    def _update_temperature(self, val):
        """Update domain temperature"""
        # Update covariance scaling for all domains
        self._update_visualization()
    
    def _simulation_step(self):
        """Execute one step of Graph-Manifold dynamics"""
        self.time_step += 1
        
        # Compute forces
        forces = self._compute_forces()
        
        # Update node positions (simplified 2D projection)
        eta_V = 0.01
        for i, node in enumerate(self.nodes):
            node.position += eta_V * forces[i]
            # Add some noise
            node.position += np.random.randn(2) * 0.01
        
        # Update edges (proximal step simulation)
        if self.time_step % 3 == 0:  # Edge updates every 3 node steps
            self._update_edges()
        
        # Compute and store energy
        energy = self._compute_energy()
        self.energy_history.append(energy)
        
        # Check for fission/fusion
        if self.time_step % 10 == 0:
            self._check_topology_changes()
    
    def _compute_forces(self):
        """Compute forces on nodes from all energy terms"""
        forces = np.zeros((self.num_nodes, 2))
        
        # Semantic domain forces
        if self.show_semantic_field:
            for i, node in enumerate(self.nodes):
                # Project to semantic chart (simplified as 2D position)
                p_sem = node.position
                
                for domain in self.semantic_domains:
                    for comp in domain:
                        # Compute responsibility
                        dist = multivariate_normal(comp.mean, comp.cov)
                        resp = comp.weight * dist.pdf(p_sem)
                        
                        # Force towards component mean
                        force = resp * (comp.mean - p_sem) * 0.1
                        forces[i] += force
        
        # Edge forces
        if self.show_edge_potentials:
            for edge_type, adj_matrix in self.edges.items():
                for i in range(self.num_nodes):
                    for j in range(self.num_nodes):
                        if adj_matrix[i, j] > 0:
                            # Attraction/repulsion based on edge type
                            diff = self.nodes[j].position - self.nodes[i].position
                            dist = np.linalg.norm(diff)
                            
                            if edge_type in ['SUPPORT', 'ELABORATE']:
                                # Attraction
                                force = adj_matrix[i, j] * diff * 0.05
                            else:
                                # Repulsion
                                force = -adj_matrix[i, j] * diff / (dist + 0.1) * 0.03
                            
                            forces[i] += force
        
        return forces
    
    def _update_edges(self):
        """Update edge weights with proximal operator"""
        # Simplified edge update
        for edge_type in self.edges:
            # Add noise and apply sparsity
            self.edges[edge_type] += np.random.randn(self.num_nodes, self.num_nodes) * 0.01
            self.edges[edge_type] = np.clip(self.edges[edge_type], 0, 1)
            
            # Sparsity constraint (keep top-k per row)
            k = 3
            for i in range(self.num_nodes):
                row = self.edges[edge_type][i]
                threshold = np.partition(row, -k)[-k] if np.count_nonzero(row) > k else 0
                self.edges[edge_type][i][row < threshold] = 0
    
    def _compute_energy(self):
        """Compute total energy of the system"""
        alpha = self.slider_alpha.val
        beta = self.slider_beta.val
        gamma = self.slider_gamma.val
        
        E_sem = 0
        E_edges = 0
        E_struct = 0
        
        # Simplified energy computation
        for i, node in enumerate(self.nodes):
            # Semantic energy
            for domain in self.semantic_domains:
                for comp in domain:
                    dist = multivariate_normal(comp.mean, comp.cov)
                    E_sem -= comp.weight * np.log(dist.pdf(node.position) + 1e-10)
            
            # Edge energy
            for edge_type, adj_matrix in self.edges.items():
                for j in range(self.num_nodes):
                    if adj_matrix[i, j] > 0:
                        dist = np.linalg.norm(self.nodes[i].position - self.nodes[j].position)
                        E_edges += adj_matrix[i, j] * dist**2
        
        return alpha * E_sem + beta * E_edges + gamma * E_struct
    
    def _check_topology_changes(self):
        """Check for fission/fusion conditions"""
        # Simplified: just check node distances
        merge_threshold = 0.5
        for i in range(self.num_nodes):
            for j in range(i+1, self.num_nodes):
                dist = np.linalg.norm(self.nodes[i].position - self.nodes[j].position)
                if dist < merge_threshold:
                    # Merge nodes (simplified: just average positions)
                    self.nodes[i].position = (self.nodes[i].position + self.nodes[j].position) / 2
    
    def _animation_step(self, frame):
        """Animation update function"""
        self._simulation_step()
        self._update_visualization()
        return []
    
    def _update_visualization(self):
        """Update all visualization components"""
        # Clear axes
        self.ax_main.clear()
        self.ax_energy.clear()
        self.ax_charts.clear()
        self.ax_fields.clear()
        
        # Draw semantic fields
        if self.show_semantic_field:
            self._draw_semantic_fields()
        
        # Draw structural fields  
        if self.show_structural_field:
            self._draw_structural_fields()
        
        # Draw nodes
        self._draw_nodes()
        
        # Draw edges
        if self.show_edge_potentials:
            self._draw_edges()
        
        # Draw energy landscape
        if self.show_energy_landscape:
            self._draw_energy_landscape()
        
        # Draw chart projections
        if self.show_charts:
            self._draw_chart_projections()
        
        # Draw node field details
        if self.show_node_fields:
            self._draw_node_fields()
        
        # Update energy history
        if len(self.energy_history) > 0:
            self.ax_history.clear()
            self.ax_history.plot(self.energy_history, 'b-', linewidth=2)
            self.ax_history.set_xlabel('Time Step')
            self.ax_history.set_ylabel('Total Energy')
            self.ax_history.set_title(f'Energy Evolution (Step {self.time_step})')
            self.ax_history.grid(True, alpha=0.3)
        
        # Set main plot properties
        self.ax_main.set_xlim(-6, 6)
        self.ax_main.set_ylim(-6, 6)
        self.ax_main.set_aspect('equal')
        self.ax_main.set_title('Graph-Manifold State')
        self.ax_main.grid(True, alpha=0.2)
        
        plt.draw()
    
    def _draw_semantic_fields(self):
        """Draw semantic domain GMM components"""
        x = np.linspace(-6, 6, 100)
        y = np.linspace(-6, 6, 100)
        X, Y = np.meshgrid(x, y)
        pos = np.dstack((X, Y))
        
        for domain_idx, domain in enumerate(self.semantic_domains):
            Z = np.zeros_like(X)
            for comp in domain:
                rv = multivariate_normal(comp.mean, comp.cov * self.slider_temp.val)
                Z += comp.weight * rv.pdf(pos)
            
            # Draw contours
            contour = self.ax_main.contour(X, Y, Z, levels=3, 
                                          colors=domain[0].color, alpha=0.3)
            
            # Label domain
            self.ax_main.text(domain[0].mean[0], domain[0].mean[1], 
                            f'Sem{domain_idx}', fontsize=8, 
                            color=domain[0].color, weight='bold')
    
    def _draw_structural_fields(self):
        """Draw structural domain components"""
        for domain_idx, domain in enumerate(self.structural_domains):
            for comp in domain:
                circle = Circle(comp.mean, np.sqrt(comp.cov[0,0]) * self.slider_temp.val,
                              fill=False, linestyle='--', 
                              color=comp.color, alpha=0.5, linewidth=2)
                self.ax_main.add_patch(circle)
    
    def _draw_nodes(self):
        """Draw nodes with field information"""
        for node in self.nodes:
            # Node circle
            circle = Circle(node.position, 0.3, facecolor='lightblue', 
                          edgecolor='darkblue', linewidth=2, zorder=5)
            self.ax_main.add_patch(circle)
            
            # Node ID
            self.ax_main.text(node.position[0], node.position[1], 
                            str(node.id), ha='center', va='center',
                            fontsize=10, weight='bold', zorder=6)
            
            # Role indicator (simplified)
            role_angle = np.argmax(node.V_role[:4]) * np.pi / 2
            role_indicator = FancyArrowPatch(
                node.position,
                node.position + 0.4 * np.array([np.cos(role_angle), np.sin(role_angle)]),
                arrowstyle='->', mutation_scale=10, color='green', linewidth=1.5
            )
            self.ax_main.add_patch(role_indicator)
            
            # Uncertainty visualization
            uncert_size = np.exp(node.V_uncert[0]) * 0.1
            uncert_circle = Circle(node.position, 0.3 + uncert_size, 
                                 fill=False, linestyle=':', 
                                 color='red', alpha=0.5)
            self.ax_main.add_patch(uncert_circle)
    
    def _draw_edges(self):
        """Draw typed edges with appropriate styles"""
        edge_styles = {
            'SUPPORT': {'color': 'green', 'style': '-', 'arrow': '->'},
            'ELABORATE': {'color': 'blue', 'style': '--', 'arrow': '->'},
            'CONTRADICT': {'color': 'red', 'style': '-', 'arrow': '<->'},
            'PRECEDE': {'color': 'orange', 'style': '-', 'arrow': '->'},
            'CAUSE': {'color': 'purple', 'style': '-', 'arrow': '-|>'}  # Changed from '=>' to '-|>'
        }
        
        for edge_type, adj_matrix in self.edges.items():
            style = edge_styles.get(edge_type, {'color': 'gray', 'style': '-', 'arrow': '->'})
            
            for i in range(self.num_nodes):
                for j in range(self.num_nodes):
                    if adj_matrix[i, j] > 0.1:  # Threshold for visualization
                        start = self.nodes[i].position
                        end = self.nodes[j].position
                        
                        # Offset for multiple edges
                        offset = 0.1 * (hash((i, j, edge_type)) % 3 - 1)
                        mid = (start + end) / 2 + np.array([-offset*(end[1]-start[1]), 
                                                           offset*(end[0]-start[0])])
                        
                        arrow = FancyArrowPatch(
                            start, end,
                            connectionstyle=f"arc3,rad={offset}",
                            arrowstyle=style['arrow'],
                            color=style['color'],
                            alpha=min(adj_matrix[i, j], 0.8),
                            linewidth=1 + adj_matrix[i, j] * 2,
                            linestyle=style['style'],
                            zorder=1
                        )
                        self.ax_main.add_patch(arrow)
    
    def _draw_energy_landscape(self):
        """Draw 3D energy landscape"""
        # Create grid
        x = np.linspace(-6, 6, 50)
        y = np.linspace(-6, 6, 50)
        X, Y = np.meshgrid(x, y)
        Z = np.zeros_like(X)
        
        # Compute energy at each point
        for i in range(X.shape[0]):
            for j in range(X.shape[1]):
                pos = np.array([X[i, j], Y[i, j]])
                
                # Semantic energy
                for domain in self.semantic_domains:
                    for comp in domain:
                        dist = multivariate_normal(comp.mean, comp.cov)
                        Z[i, j] -= comp.weight * np.log(dist.pdf(pos) + 1e-10)
        
        # Plot surface
        self.ax_energy.plot_surface(X, Y, Z, cmap='viridis', alpha=0.6)
        
        # Plot node positions
        for node in self.nodes:
            self.ax_energy.scatter(node.position[0], node.position[1], 0,
                                 color='red', s=50, marker='o')
        
        self.ax_energy.set_xlabel('X')
        self.ax_energy.set_ylabel('Y')
        self.ax_energy.set_zlabel('Energy')
        self.ax_energy.set_title('Energy Landscape')
    
    def _draw_chart_projections(self):
        """Visualize different chart projections"""
        # Create subplots for different charts
        charts = ['P_sem', 'P_role', 'P_val']
        colors = ['blue', 'green', 'red']
        
        for idx, (chart, color) in enumerate(zip(charts, colors)):
            # Simplified: just show node positions with different transformations
            transformed_positions = []
            for node in self.nodes:
                if chart == 'P_sem':
                    pos = node.position
                elif chart == 'P_role':
                    # Rotate based on role
                    angle = np.sum(node.V_role[:2])
                    rot = np.array([[np.cos(angle), -np.sin(angle)],
                                   [np.sin(angle), np.cos(angle)]])
                    pos = rot @ node.position
                else:  # P_val
                    # Scale based on valence
                    scale = 1 + 0.5 * np.tanh(node.V_mod[0])
                    pos = node.position * scale
                
                transformed_positions.append(pos)
                
            # Plot
            positions = np.array(transformed_positions)
            self.ax_charts.scatter(positions[:, 0], positions[:, 1], 
                                 c=color, alpha=0.6, label=chart, s=30)
        
        self.ax_charts.legend()
        self.ax_charts.set_xlabel('Dim 1')
        self.ax_charts.set_ylabel('Dim 2')
        self.ax_charts.grid(True, alpha=0.3)
    
    def _draw_node_fields(self):
        """Visualize node field details"""
        if len(self.nodes) > 0:
            # Select first node for detailed view
            node = self.nodes[0]
            
            fields = ['V_sem', 'V_role', 'V_mod', 'V_time', 'V_uncert', 'V_type']
            field_data = [
                node.V_sem[:10],  # First 10 dims
                node.V_role[:8],
                node.V_mod[:8],
                node.V_time[:8],
                node.V_uncert,
                node.V_type[:8]
            ]
            
            y_pos = 0.9
            for field, data in zip(fields, field_data):
                # Create mini heatmap
                self.ax_fields.text(0.05, y_pos, field + ':', fontsize=10, 
                                  transform=self.ax_fields.transAxes)
                
                # Plot values as bars
                x = np.arange(len(data))
                bar_height = 0.08
                bars = self.ax_fields.barh(y_pos - bar_height/2, np.abs(data).sum(), 
                                          height=bar_height, 
                                          transform=self.ax_fields.transAxes)
                
                y_pos -= 0.15
            
            self.ax_fields.set_xlim(0, 1)
            self.ax_fields.set_ylim(0, 1)
            self.ax_fields.axis('off')
            self.ax_fields.set_title(f'Node {node.id} Fields')
    
    def run(self):
        """Start the visualization"""
        plt.show()

if __name__ == "__main__":
    visualizer = MinervaVisualizer(num_nodes=8)
    visualizer.run()
