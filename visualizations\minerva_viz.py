import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import FancyBboxPatch, Circle, FancyArrowPatch, Ellipse
from matplotlib.widgets import Check<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>
from matplotlib.animation import FuncAnimation
import matplotlib.gridspec as gridspec
from mpl_toolkits.mplot3d import Axes3D
import networkx as nx
from scipy.stats import multivariate_normal
from scipy.linalg import chole<PERSON>, LinAlgError
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Union
import colorsys
import warnings
warnings.filterwarnings('ignore')

# Try to import sklearn, fall back to simple PCA if not available
try:
    from sklearn.decomposition import PCA
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: sklearn not available. Semantic trajectory visualization will be simplified.")

@dataclass
class ConceptState:
    """Represents the conceptual interpretation of a node"""
    concept_label: str
    confidence: float
    semantic_anchors: List[str]  # Top semantic components
    kb_links: List[str]  # Knowledge base connections
    modality: str  # negation, certainty, etc.
    temporal_aspect: str  # tense, aspect
    discourse_role: str  # premise, conclusion, evidence, etc.

@dataclass
class NodeState:
    """Represents a node in the Graph-Manifold with factorized fields"""
    id: int
    V_sem: np.ndarray      # Semantic embedding (D=128)
    V_role: np.ndarray     # Role/discourse features (d_r=32)
    V_mod: np.ndarray      # Modality/negation/polarity (d_m=16)
    V_time: np.ndarray     # Temporal features (d_t=16)
    V_uncert: np.ndarray   # Heteroscedastic log-variances (2D)
    V_type: np.ndarray     # Type/sense logits (d_ty=24)
    V_link: Optional[np.ndarray] = None  # KB/entity/link features
    position: np.ndarray = None  # 2D position for visualization

    # Chart projections (computed dynamically)
    P_sem_proj: Optional[np.ndarray] = None    # Semantic chart projection
    P_role_proj: Optional[np.ndarray] = None   # Role chart projection
    P_val_proj: Optional[np.ndarray] = None    # Valence chart projection
    P_str_proj: Optional[np.ndarray] = None    # Structural chart projection

    # Responsibilities and forces
    semantic_responsibilities: Optional[np.ndarray] = None
    structural_responsibilities: Optional[np.ndarray] = None
    current_force: Optional[np.ndarray] = None
    tension: float = 0.0

    # Concept evolution tracking
    concept_history: List[ConceptState] = None
    trajectory: List[np.ndarray] = None  # Position history
    semantic_trajectory: List[np.ndarray] = None  # Semantic embedding history
    link_evolution: Dict[str, List[float]] = None  # How links change over time

@dataclass
class DomainComponent:
    """Gaussian component in a GMM domain family"""
    mean: np.ndarray
    cov: np.ndarray
    weight: float
    color: str
    temperature: float = 1.0
    active: bool = True

@dataclass
class TypedMetric:
    """Relation-specific metric M_τ = L_τ L_τ^T + εI"""
    relation_type: str
    L_matrix: np.ndarray  # Low-rank component
    epsilon: float = 0.01  # Diagonal regularization
    eigenvalue_floor: float = 0.01
    eigenvalue_cap: float = 10.0

    def get_metric(self) -> np.ndarray:
        """Compute positive definite metric"""
        M = self.L_matrix @ self.L_matrix.T + self.epsilon * np.eye(self.L_matrix.shape[0])
        # Eigenvalue clamping for numerical stability
        eigenvals, eigenvecs = np.linalg.eigh(M)
        eigenvals = np.clip(eigenvals, self.eigenvalue_floor, self.eigenvalue_cap)
        return eigenvecs @ np.diag(eigenvals) @ eigenvecs.T

@dataclass
class ChartProjection:
    """Chart projection P: R^D_all → R^d'"""
    name: str
    input_fields: List[str]  # Which node fields to use
    projection_matrix: np.ndarray
    output_dim: int

class MinervaVisualizer:
    """Enhanced visualizer for Minerva 0.17 Graph-Manifold architecture"""

    def __init__(self, num_nodes=12, seed=42):
        np.random.seed(seed)
        self.num_nodes = num_nodes

        # Initialize concept tracking first (needed for node initialization)
        self.concept_vocabulary = self._initialize_concept_vocabulary()
        self.kb_entities = self._initialize_kb_entities()

        # Initialize core components
        self.nodes = self._initialize_nodes()
        self.edges = self._initialize_edges()
        self.semantic_domains = self._initialize_semantic_domains()
        self.structural_domains = self._initialize_structural_domains()
        self.typed_metrics = self._initialize_typed_metrics()
        self.chart_projections = self._initialize_chart_projections()

        # Energy parameters (matching spec)
        self.alpha = 1.0  # E_sem weight
        self.beta = 1.0   # E_edges weight
        self.gamma = 0.5  # E_struct weight
        self.rho = 0.3    # E_rules weight
        self.zeta_1 = 0.1 # E_dist_align weight
        self.zeta_2 = 0.1 # E_resp_align weight

        # Simulation parameters
        self.eta_V = 0.01  # Node learning rate
        self.eta_A = 0.005 # Edge learning rate
        self.alternation_ratio = 3  # Node:Edge update ratio
        self.edge_cooldown_steps = 10
        self.edge_cooldown_counter = 0

        # Visualization state
        self.show_semantic_field = True
        self.show_structural_field = True
        self.show_edge_potentials = True
        self.show_valence_modulation = True
        self.show_node_fields = True
        self.show_energy_landscape = True
        self.show_charts = True
        self.show_responsibilities = True
        self.show_tensions = True
        self.show_concept_evolution = True
        self.show_trajectories = True
        self.show_link_evolution = True
        self.animation_running = False
        self.time_step = 0



        # Energy and diagnostics tracking
        self.energy_history = {'total': [], 'semantic': [], 'edges': [], 'structural': [], 'rules': [], 'align': []}
        self.tension_history = []
        self.convergence_metrics = {'node_change': [], 'edge_change': [], 'active_domains': []}

        self._setup_figure()
        self._setup_controls()
        self._update_visualization()
        
    def _initialize_nodes(self):
        """Initialize factorized node states in the Graph-Manifold"""
        nodes = []
        # Create nodes in a circle initially for visualization
        angles = np.linspace(0, 2*np.pi, self.num_nodes, endpoint=False)

        for i in range(self.num_nodes):
            # Initialize factorized fields according to spec
            node = NodeState(
                id=i,
                V_sem=np.random.randn(128) * 0.5,     # Semantic content (D=128)
                V_role=np.random.randn(32) * 0.3,     # Discourse roles (d_r=32)
                V_mod=np.random.randn(16) * 0.2,      # Modality/negation (d_m=16)
                V_time=np.random.randn(16) * 0.2,     # Temporal features (d_t=16)
                V_uncert=np.random.randn(2) * 0.1,    # Heteroscedastic log-vars
                V_type=np.random.randn(24) * 0.3,     # Type/sense logits (d_ty=24)
                V_link=np.random.randn(8) * 0.1,      # KB/entity features (optional)
                position=np.array([np.cos(angles[i])*3, np.sin(angles[i])*3])
            )

            # Initialize responsibilities and forces
            node.semantic_responsibilities = np.zeros(15)  # 5 domains × 3 components
            node.structural_responsibilities = np.zeros(6)  # 3 domains × 2 components
            node.current_force = np.zeros(2)
            node.tension = 0.0

            # Initialize concept evolution tracking
            node.concept_history = []
            node.trajectory = [node.position.copy()]
            node.semantic_trajectory = [node.V_sem.copy()]
            node.link_evolution = {}

            # Initialize with a starting concept
            initial_concept = self._generate_concept_from_node(node)
            node.concept_history.append(initial_concept)

            nodes.append(node)
        return nodes
    
    def _initialize_edges(self):
        """Initialize typed edge weights A_τ according to Graph-Manifold spec"""
        edge_types = ['SUPPORT', 'ELABORATE', 'CONTRADICT', 'PRECEDE', 'CAUSE', 'COREF']
        edges = {}

        for edge_type in edge_types:
            # Initialize with small random weights
            adj_matrix = np.random.exponential(0.1, (self.num_nodes, self.num_nodes))

            # Apply sparsity constraints (top-k per row)
            k = 3  # Sparsity budget per node
            for i in range(self.num_nodes):
                row = adj_matrix[i]
                if np.count_nonzero(row) > k:
                    threshold = np.partition(row, -k)[-k]
                    adj_matrix[i][row < threshold] = 0

            # Zero diagonal and apply type-specific constraints
            np.fill_diagonal(adj_matrix, 0)

            # Type-specific initialization patterns
            if edge_type in ['SUPPORT', 'ELABORATE']:
                # These tend to be more common
                adj_matrix *= 1.5
            elif edge_type == 'CONTRADICT':
                # Contradictions are rarer
                adj_matrix *= 0.5
            elif edge_type in ['PRECEDE', 'CAUSE']:
                # Directional edges - make asymmetric
                adj_matrix = np.triu(adj_matrix) * 1.2

            edges[edge_type] = adj_matrix

        return edges

    def _initialize_typed_metrics(self):
        """Initialize relation-specific metrics M_τ = L_τ L_τ^T + εI"""
        edge_types = ['SUPPORT', 'ELABORATE', 'CONTRADICT', 'PRECEDE', 'CAUSE', 'COREF']
        metrics = {}

        for edge_type in edge_types:
            # Low-rank component L_τ (rank 4 for 2D visualization)
            L_matrix = np.random.randn(2, 4) * 0.3

            metrics[edge_type] = TypedMetric(
                relation_type=edge_type,
                L_matrix=L_matrix,
                epsilon=0.01,
                eigenvalue_floor=0.01,
                eigenvalue_cap=5.0
            )

        return metrics

    def _initialize_chart_projections(self):
        """Initialize chart projections P_sem, P_τ, P_val, P_str"""
        projections = {}

        # Semantic chart: P_sem: R^128 → R^64
        projections['P_sem'] = ChartProjection(
            name='P_sem',
            input_fields=['V_sem'],
            projection_matrix=np.random.randn(64, 128) * 0.1,
            output_dim=64
        )

        # Valence chart: P_val: R^(32+16+2) → R^32
        projections['P_val'] = ChartProjection(
            name='P_val',
            input_fields=['V_role', 'V_mod', 'V_uncert'],
            projection_matrix=np.random.randn(32, 50) * 0.1,  # 32+16+2=50
            output_dim=32
        )

        # Structural chart: P_str: R^(128+32+16+8) → R^64
        projections['P_str'] = ChartProjection(
            name='P_str',
            input_fields=['V_sem', 'V_role', 'V_time', 'V_link'],
            projection_matrix=np.random.randn(64, 184) * 0.1,  # 128+32+16+8=184
            output_dim=64
        )

        # Relation-specific charts: P_τ for each edge type
        for edge_type in ['SUPPORT', 'ELABORATE', 'CONTRADICT', 'PRECEDE', 'CAUSE', 'COREF']:
            projections[f'P_{edge_type}'] = ChartProjection(
                name=f'P_{edge_type}',
                input_fields=['V_sem', 'V_role', 'V_mod', 'V_time'],  # Default per spec
                projection_matrix=np.random.randn(32, 192) * 0.1,  # 128+32+16+16=192
                output_dim=32
            )

        return projections

    def _initialize_concept_vocabulary(self):
        """Initialize vocabulary of concepts that nodes can represent"""
        return {
            'semantic_concepts': [
                'causation', 'correlation', 'evidence', 'hypothesis', 'conclusion',
                'premise', 'assumption', 'fact', 'opinion', 'belief', 'knowledge',
                'uncertainty', 'possibility', 'necessity', 'contradiction',
                'support', 'opposition', 'elaboration', 'example', 'analogy',
                'definition', 'classification', 'comparison', 'contrast', 'sequence'
            ],
            'discourse_roles': [
                'claim', 'evidence', 'warrant', 'backing', 'qualifier', 'rebuttal',
                'concession', 'refutation', 'elaboration', 'example', 'summary',
                'introduction', 'transition', 'conclusion'
            ],
            'modalities': [
                'certain', 'probable', 'possible', 'unlikely', 'impossible',
                'necessary', 'contingent', 'negated', 'affirmed', 'conditional'
            ],
            'temporal_aspects': [
                'past', 'present', 'future', 'ongoing', 'completed', 'habitual',
                'instantaneous', 'durative', 'iterative', 'sequential', 'simultaneous'
            ]
        }

    def _initialize_kb_entities(self):
        """Initialize knowledge base entities that can be linked"""
        return {
            'scientific_concepts': [
                'gravity', 'evolution', 'photosynthesis', 'DNA', 'quantum_mechanics',
                'relativity', 'thermodynamics', 'electromagnetism', 'cell_division',
                'natural_selection', 'climate_change', 'biodiversity'
            ],
            'philosophical_concepts': [
                'consciousness', 'free_will', 'determinism', 'causality', 'identity',
                'existence', 'knowledge', 'truth', 'justice', 'morality', 'ethics'
            ],
            'mathematical_concepts': [
                'infinity', 'probability', 'statistics', 'calculus', 'geometry',
                'algebra', 'logic', 'set_theory', 'topology', 'number_theory'
            ],
            'social_concepts': [
                'democracy', 'capitalism', 'socialism', 'culture', 'society',
                'institution', 'power', 'authority', 'legitimacy', 'cooperation'
            ]
        }

    def _generate_concept_from_node(self, node):
        """Generate a concept interpretation from current node state"""
        # Use semantic embedding to select concept
        sem_hash = hash(tuple(node.V_sem[:10].astype(int))) % len(self.concept_vocabulary['semantic_concepts'])
        concept_label = self.concept_vocabulary['semantic_concepts'][sem_hash]

        # Use role features for discourse role
        role_idx = np.argmax(node.V_role[:len(self.concept_vocabulary['discourse_roles'])])
        discourse_role = self.concept_vocabulary['discourse_roles'][role_idx]

        # Use modality features
        mod_idx = np.argmax(node.V_mod[:len(self.concept_vocabulary['modalities'])])
        modality = self.concept_vocabulary['modalities'][mod_idx]

        # Use temporal features
        time_idx = np.argmax(node.V_time[:len(self.concept_vocabulary['temporal_aspects'])])
        temporal_aspect = self.concept_vocabulary['temporal_aspects'][time_idx]

        # Generate KB links based on V_link
        kb_links = []
        if node.V_link is not None:
            for entities in self.kb_entities.values():
                # Ensure we don't exceed V_link dimensions
                n_entities = min(len(entities), len(node.V_link))
                if n_entities > 0:
                    link_strength = np.dot(node.V_link[:n_entities], np.ones(n_entities))
                    if link_strength > 0.5:
                        top_entity_idx = np.argmax(node.V_link[:n_entities])
                        kb_links.append(entities[top_entity_idx])

        # Get top semantic anchors (simplified)
        top_sem_indices = np.argsort(node.semantic_responsibilities)[-3:]
        semantic_anchors = [f"sem_comp_{i}" for i in top_sem_indices]

        # Calculate confidence based on uncertainty
        confidence = 1.0 / (1.0 + np.exp(node.V_uncert[0]))  # Sigmoid of negative log-variance

        return ConceptState(
            concept_label=concept_label,
            confidence=confidence,
            semantic_anchors=semantic_anchors,
            kb_links=kb_links,
            modality=modality,
            temporal_aspect=temporal_aspect,
            discourse_role=discourse_role
        )
    
    def _initialize_semantic_domains(self):
        """Initialize Semantic GMM family in P_sem chart (64D projected to 2D for viz)"""
        domains = []
        colors = ['red', 'blue', 'green', 'purple', 'orange']

        for domain_idx in range(5):  # 5 semantic domains
            components = []
            for comp_idx in range(3):  # 3 components per domain (M=3)
                # Initialize in 2D for visualization (would be 64D in P_sem chart)
                mean_2d = np.random.randn(2) * 2 + np.array([domain_idx*2-4, comp_idx*1.5-1.5])

                # Create covariance with some correlation structure
                angle = np.random.uniform(0, 2*np.pi)
                eigenvals = np.random.uniform(0.3, 1.2, 2)
                rotation = np.array([[np.cos(angle), -np.sin(angle)],
                                   [np.sin(angle), np.cos(angle)]])
                cov_2d = rotation @ np.diag(eigenvals) @ rotation.T

                comp = DomainComponent(
                    mean=mean_2d,
                    cov=cov_2d,
                    weight=np.random.uniform(0.2, 0.4),
                    color=colors[domain_idx],
                    temperature=1.0,
                    active=True
                )
                components.append(comp)
            domains.append(components)
        return domains

    def _initialize_structural_domains(self):
        """Initialize Structural GMM family in P_str chart"""
        domains = []
        colors = ['cyan', 'magenta', 'yellow']
        structural_patterns = ['linear', 'clustered', 'hierarchical']

        for domain_idx in range(3):  # 3 structural patterns
            components = []
            pattern = structural_patterns[domain_idx]

            for comp_idx in range(2):  # 2 components per domain
                if pattern == 'linear':
                    # Linear arrangement pattern
                    mean_2d = np.array([comp_idx*4-2, 0]) + np.random.randn(2)*0.5
                elif pattern == 'clustered':
                    # Clustered pattern
                    mean_2d = np.random.randn(2) * 1.5 + np.array([0, domain_idx*2-2])
                else:  # hierarchical
                    # Hierarchical pattern
                    mean_2d = np.array([comp_idx*2-1, domain_idx*3-3]) + np.random.randn(2)*0.3

                # Larger covariance for structural domains
                eigenvals = np.random.uniform(0.8, 2.0, 2)
                angle = np.random.uniform(0, 2*np.pi)
                rotation = np.array([[np.cos(angle), -np.sin(angle)],
                                   [np.sin(angle), np.cos(angle)]])
                cov_2d = rotation @ np.diag(eigenvals) @ rotation.T

                comp = DomainComponent(
                    mean=mean_2d,
                    cov=cov_2d,
                    weight=0.5,
                    color=colors[domain_idx],
                    temperature=1.0,
                    active=True
                )
                components.append(comp)
            domains.append(components)
        return domains
    
    def _setup_figure(self):
        """Setup the matplotlib figure with comprehensive subplots including concept evolution"""
        self.fig = plt.figure(figsize=(28, 18))
        self.fig.suptitle('Minerva 0.17 Graph-Manifold: Concept Evolution in Semantic Space', fontsize=20, fontweight='bold')

        # Create enhanced grid layout with more space for concept tracking
        gs = gridspec.GridSpec(5, 6, figure=self.fig, hspace=0.4, wspace=0.3)

        # Main graph visualization (larger)
        self.ax_main = self.fig.add_subplot(gs[:2, :2])
        self.ax_main.set_title('Graph-Manifold State with Concept Trajectories', fontsize=14)
        self.ax_main.set_xlim(-6, 6)
        self.ax_main.set_ylim(-6, 6)
        self.ax_main.set_aspect('equal')
        self.ax_main.grid(True, alpha=0.2)

        # Energy landscape (3D)
        self.ax_energy = self.fig.add_subplot(gs[0, 2], projection='3d')
        self.ax_energy.set_title('Energy Landscape E(V̄,A)', fontsize=12)

        # Chart projections
        self.ax_charts = self.fig.add_subplot(gs[1, 2])
        self.ax_charts.set_title('Chart Projections (P_sem, P_val, P_str)', fontsize=12)

        # Concept evolution timeline
        self.ax_concepts = self.fig.add_subplot(gs[0, 3])
        self.ax_concepts.set_title('Concept Evolution Timeline', fontsize=12)

        # Link evolution tracking
        self.ax_links = self.fig.add_subplot(gs[1, 3])
        self.ax_links.set_title('Knowledge Base Link Evolution', fontsize=12)

        # Semantic trajectory space
        self.ax_sem_traj = self.fig.add_subplot(gs[0, 4])
        self.ax_sem_traj.set_title('Semantic Trajectory (PCA)', fontsize=12)

        # Responsibilities visualization
        self.ax_resp = self.fig.add_subplot(gs[1, 4])
        self.ax_resp.set_title('Domain Responsibilities γ', fontsize=12)

        # Tension and convergence metrics
        self.ax_tension = self.fig.add_subplot(gs[0, 5])
        self.ax_tension.set_title('Tension & Convergence', fontsize=12)

        # Concept confidence over time
        self.ax_confidence = self.fig.add_subplot(gs[1, 5])
        self.ax_confidence.set_title('Concept Confidence', fontsize=12)

        # Energy history (detailed breakdown)
        self.ax_history = self.fig.add_subplot(gs[2, :3])
        self.ax_history.set_title('Energy Evolution (α·E_sem + β·E_edges + γ·E_struct + ρ·E_rules + ζ·E_align)', fontsize=12)
        self.ax_history.set_xlabel('Time Step')
        self.ax_history.set_ylabel('Energy')

        # Node field details
        self.ax_fields = self.fig.add_subplot(gs[3, :2])
        self.ax_fields.set_title('Factorized Node Fields (V_sem, V_role, V_mod, V_time, V_uncert, V_type)', fontsize=12)

        # Typed metrics visualization
        self.ax_metrics = self.fig.add_subplot(gs[3, 2])
        self.ax_metrics.set_title('Typed Metrics M_τ', fontsize=12)

        # Current concept states
        self.ax_current_concepts = self.fig.add_subplot(gs[2:4, 3:5])
        self.ax_current_concepts.set_title('Current Concept States & Interpretations', fontsize=12)
        self.ax_current_concepts.axis('off')

        # Concept transition matrix
        self.ax_transitions = self.fig.add_subplot(gs[4, :3])
        self.ax_transitions.set_title('Concept Transition Patterns', fontsize=12)

        # Link strength heatmap
        self.ax_link_matrix = self.fig.add_subplot(gs[4, 3:5])
        self.ax_link_matrix.set_title('KB Link Strength Matrix', fontsize=12)

        # Mathematical formulas and parameters
        self.ax_math = self.fig.add_subplot(gs[2:, 5])
        self.ax_math.axis('off')
        self._draw_formulas()
    
    def _draw_formulas(self):
        """Draw key mathematical formulas"""
        formulas = [
            r'$E(\bar{V}, A) = \alpha E_{sem} + \beta E_{edges} + \gamma E_{struct} + \rho E_{rules} + \zeta E_{align}$',
            '',
            r'Semantic Force: $F_{sem}(k) = -\frac{\partial E_{sem}}{\partial \bar{V}(k)}$',
            r'$= \sum_j g_{sem}(k,j) \sum_i \gamma_{j,i}(k) \cdot (-P_{j,i}(P_{sem}(V_{sem}(k)) - \mu_{j,i}))$',
            '',
            r'Typed Metric: $M_\tau = L_\tau L_\tau^T + \epsilon I$',
            r'Attraction: $U^{attr}_\tau(i,j) = w_\tau \cdot ||P_\tau(\bar{V}(i)) - P_\tau(\bar{V}(j))||^2_{M_\tau}$',
            r'Repulsion: $U^{rep}_\tau(i,j) = w_\tau \cdot \exp(-||P_\tau(\bar{V}(i)) - P_\tau(\bar{V}(j))||^2_{M_\tau} / \sigma_\tau^2)$',
            '',
            r'Node Update: $\bar{V}_{t+1} = FieldNorm(\bar{V}_t - \eta_{V,t} \cdot clip(\frac{\partial E}{\partial \bar{V}_t}, -g_{clip}, g_{clip}))$',
            r'Edge Update: $A_{\tau,t+1} = Prox_{\lambda, rules}(A_{\tau,t} - \eta_{A,t} \cdot clip(\frac{\partial E}{\partial A_{\tau,t}}, -g_{clip}, g_{clip}))$',
            '',
            r'Tension: $T = Trace(Cov_k(-\frac{\partial E}{\partial \bar{V}_k}))$',
            r'Fission trigger: $T > \tau_{tension}$ for S steps'
        ]
        
        y_pos = 0.95
        for formula in formulas:
            if formula:
                self.ax_math.text(0.05, y_pos, formula, fontsize=10, 
                                 transform=self.ax_math.transAxes, 
                                 verticalalignment='top')
            y_pos -= 0.06
    
    def _setup_controls(self):
        """Setup enhanced interactive controls"""
        # Checkbox for component visibility (moved to accommodate larger figure)
        ax_check = plt.axes([0.85, 0.5, 0.13, 0.35])
        labels = ['Semantic\nFields', 'Structural\nFields', 'Edge\nPotentials',
                 'Valence\nModulation', 'Node\nFields', 'Energy\nLandscape',
                 'Charts', 'Responsibilities', 'Tensions', 'Concept\nEvolution',
                 'Trajectories', 'Link\nEvolution']
        visibility = [True] * 12
        self.check_buttons = CheckButtons(ax_check, labels, visibility)
        self.check_buttons.on_clicked(self._toggle_visibility)

        # Animation controls
        ax_play = plt.axes([0.85, 0.4, 0.05, 0.04])
        self.btn_play = Button(ax_play, 'Play')
        self.btn_play.on_clicked(self._toggle_animation)

        ax_step = plt.axes([0.91, 0.4, 0.05, 0.04])
        self.btn_step = Button(ax_step, 'Step')
        self.btn_step.on_clicked(self._single_step)

        # Reset button
        ax_reset = plt.axes([0.85, 0.35, 0.11, 0.04])
        self.btn_reset = Button(ax_reset, 'Reset System')
        self.btn_reset.on_clicked(self._reset_system)

        # Concept tracking controls
        ax_track_concepts = plt.axes([0.85, 0.3, 0.11, 0.04])
        self.btn_track_concepts = Button(ax_track_concepts, 'Track Concepts')
        self.btn_track_concepts.on_clicked(self._toggle_concept_tracking)

        # Temperature slider
        ax_temp = plt.axes([0.85, 0.25, 0.11, 0.03])
        self.slider_temp = Slider(ax_temp, 'Temperature', 0.1, 3.0, valinit=1.0)
        self.slider_temp.on_changed(self._update_temperature)

        # Energy weight sliders
        ax_alpha = plt.axes([0.85, 0.2, 0.11, 0.03])
        self.slider_alpha = Slider(ax_alpha, 'α (E_sem)', 0.0, 3.0, valinit=1.0)
        self.slider_alpha.on_changed(self._update_energy_weights)

        ax_beta = plt.axes([0.85, 0.15, 0.11, 0.03])
        self.slider_beta = Slider(ax_beta, 'β (E_edges)', 0.0, 3.0, valinit=1.0)
        self.slider_beta.on_changed(self._update_energy_weights)

        ax_gamma = plt.axes([0.85, 0.1, 0.11, 0.03])
        self.slider_gamma = Slider(ax_gamma, 'γ (E_struct)', 0.0, 3.0, valinit=0.5)
        self.slider_gamma.on_changed(self._update_energy_weights)

        # Learning rate sliders
        ax_eta_v = plt.axes([0.85, 0.05, 0.11, 0.03])
        self.slider_eta_v = Slider(ax_eta_v, 'η_V (nodes)', 0.001, 0.1, valinit=0.01)
        self.slider_eta_v.on_changed(self._update_learning_rates)

        ax_eta_a = plt.axes([0.85, 0.01, 0.11, 0.03])
        self.slider_eta_a = Slider(ax_eta_a, 'η_A (edges)', 0.001, 0.05, valinit=0.005)
        self.slider_eta_a.on_changed(self._update_learning_rates)
    
    def _toggle_visibility(self, label):
        """Toggle component visibility"""
        if 'Semantic' in label:
            self.show_semantic_field = not self.show_semantic_field
        elif 'Structural' in label:
            self.show_structural_field = not self.show_structural_field
        elif 'Edge' in label:
            self.show_edge_potentials = not self.show_edge_potentials
        elif 'Valence' in label:
            self.show_valence_modulation = not self.show_valence_modulation
        elif 'Node' in label:
            self.show_node_fields = not self.show_node_fields
        elif 'Energy' in label:
            self.show_energy_landscape = not self.show_energy_landscape
        elif 'Charts' in label:
            self.show_charts = not self.show_charts
        elif 'Responsibilities' in label:
            self.show_responsibilities = not self.show_responsibilities
        elif 'Tensions' in label:
            self.show_tensions = not self.show_tensions
        elif 'Concept' in label:
            self.show_concept_evolution = not self.show_concept_evolution
        elif 'Trajectories' in label:
            self.show_trajectories = not self.show_trajectories
        elif 'Link' in label:
            self.show_link_evolution = not self.show_link_evolution
        self._update_visualization()

    def _toggle_concept_tracking(self, event):
        """Toggle detailed concept tracking"""
        _ = event  # Suppress unused parameter warning
        # Toggle between different concept tracking modes
        self.show_concept_evolution = not self.show_concept_evolution
        self.show_trajectories = not self.show_trajectories
        self.show_link_evolution = not self.show_link_evolution
        self._update_visualization()

    def _toggle_animation(self, event):
        """Toggle simulation animation"""
        _ = event  # Suppress unused parameter warning
        self.animation_running = not self.animation_running
        if self.animation_running:
            self.btn_play.label.set_text('Pause')
            self.animation = FuncAnimation(self.fig, self._animation_step,
                                         interval=100, blit=False)
        else:
            self.btn_play.label.set_text('Play')
            if hasattr(self, 'animation'):
                self.animation.event_source.stop()

    def _single_step(self, event):
        """Execute single simulation step"""
        _ = event  # Suppress unused parameter warning
        self._simulation_step()
        self._update_visualization()

    def _reset_system(self, event):
        """Reset the entire system to initial state"""
        _ = event  # Suppress unused parameter warning
        self.time_step = 0
        self.nodes = self._initialize_nodes()
        self.edges = self._initialize_edges()
        self.energy_history = {'total': [], 'semantic': [], 'edges': [], 'structural': [], 'rules': [], 'align': []}
        self.tension_history = []
        self.convergence_metrics = {'node_change': [], 'edge_change': [], 'active_domains': []}
        self._update_visualization()

    def _update_temperature(self, val):
        """Update domain temperature for all components"""
        _ = val  # Suppress unused parameter warning
        temp = self.slider_temp.val
        for domain in self.semantic_domains:
            for comp in domain:
                comp.temperature = temp
        for domain in self.structural_domains:
            for comp in domain:
                comp.temperature = temp
        self._update_visualization()

    def _update_energy_weights(self, val):
        """Update energy term weights"""
        _ = val  # Suppress unused parameter warning
        self.alpha = self.slider_alpha.val
        self.beta = self.slider_beta.val
        self.gamma = self.slider_gamma.val

    def _update_learning_rates(self, val):
        """Update learning rates"""
        _ = val  # Suppress unused parameter warning
        self.eta_V = self.slider_eta_v.val
        self.eta_A = self.slider_eta_a.val
    
    def _simulation_step(self):
        """Execute one step of Graph-Manifold alternating dynamics"""
        self.time_step += 1

        # Update chart projections for all nodes
        self._update_chart_projections()

        # Compute responsibilities for all domains
        self._compute_responsibilities()

        # Compute forces from all energy terms
        forces = self._compute_forces()

        # Node update with FieldNorm (simplified as position update)
        for i, node in enumerate(self.nodes):
            # Store previous state for trajectory tracking
            prev_position = node.position.copy()
            prev_V_sem = node.V_sem.copy()

            # Gradient clipping
            clipped_force = np.clip(forces[i], -5.0, 5.0)

            # Update position (representing node state in 2D projection)
            node.position += self.eta_V * clipped_force
            node.current_force = forces[i]

            # Update semantic embedding based on forces (simplified evolution)
            sem_update = np.random.randn(128) * 0.01 * np.linalg.norm(clipped_force)
            node.V_sem += sem_update

            # Update other fields slightly based on movement
            node.V_role += np.random.randn(32) * 0.005 * np.linalg.norm(clipped_force)
            node.V_mod += np.random.randn(16) * 0.003 * np.linalg.norm(clipped_force)
            node.V_time += np.random.randn(16) * 0.003 * np.linalg.norm(clipped_force)

            # Update link features based on semantic changes
            if node.V_link is not None:
                link_update = np.random.randn(8) * 0.002 * np.linalg.norm(sem_update[:8])
                node.V_link += link_update

            # Track trajectory
            node.trajectory.append(node.position.copy())
            node.semantic_trajectory.append(node.V_sem.copy())

            # Limit trajectory history
            max_history = 50
            if len(node.trajectory) > max_history:
                node.trajectory = node.trajectory[-max_history:]
                node.semantic_trajectory = node.semantic_trajectory[-max_history:]

            # Generate new concept if significant change occurred
            position_change = np.linalg.norm(node.position - prev_position)
            semantic_change = np.linalg.norm(node.V_sem - prev_V_sem)

            if position_change > 0.1 or semantic_change > 0.5:
                new_concept = self._generate_concept_from_node(node)
                node.concept_history.append(new_concept)

                # Update link evolution tracking
                for link in new_concept.kb_links:
                    if link not in node.link_evolution:
                        node.link_evolution[link] = []
                    node.link_evolution[link].append(new_concept.confidence)

                # Limit concept history
                if len(node.concept_history) > 20:
                    node.concept_history = node.concept_history[-20:]

            # Compute tension (trace of force covariance)
            if len(self.convergence_metrics['node_change']) > 5:
                recent_forces = np.array([forces[i] for _ in range(min(5, len(self.convergence_metrics['node_change'])))])
                node.tension = np.trace(np.cov(recent_forces.T)) if recent_forces.shape[0] > 1 else 0.0

        # Edge updates (alternating schedule: every alternation_ratio node steps)
        if self.edge_cooldown_counter <= 0 and self.time_step % self.alternation_ratio == 0:
            self._update_edges()
            self.edge_cooldown_counter = self.edge_cooldown_steps
        else:
            self.edge_cooldown_counter = max(0, self.edge_cooldown_counter - 1)

        # Compute and store detailed energy breakdown
        energy_breakdown = self._compute_energy_breakdown()
        for key, value in energy_breakdown.items():
            self.energy_history[key].append(value)

        # Store convergence metrics
        node_change = np.mean([np.linalg.norm(forces[i]) for i in range(self.num_nodes)])
        self.convergence_metrics['node_change'].append(node_change)

        # Check for topology changes (fission/fusion)
        if self.time_step % 10 == 0:
            self._check_topology_changes()

        # Store tension history
        avg_tension = np.mean([node.tension for node in self.nodes])
        self.tension_history.append(avg_tension)
    
    def _update_chart_projections(self):
        """Update chart projections for all nodes"""
        for node in self.nodes:
            # P_sem projection (semantic chart)
            node.P_sem_proj = node.position  # Simplified 2D projection

            # P_val projection (valence modulation)
            val_input = np.concatenate([node.V_role[:8], node.V_mod[:8], node.V_uncert])
            node.P_val_proj = val_input[:2]  # Simplified to 2D

            # P_str projection (structural chart)
            str_input = np.concatenate([node.V_sem[:8], node.V_role[:8], node.V_time[:8]])
            node.P_str_proj = str_input[:2]  # Simplified to 2D

    def _compute_responsibilities(self):
        """Compute GMM responsibilities for all nodes and domains"""
        for node in self.nodes:
            # Semantic responsibilities
            sem_resp = []
            for domain in self.semantic_domains:
                for comp in domain:
                    if comp.active:
                        # Temperature-scaled covariance
                        temp_cov = comp.cov * comp.temperature
                        dist = multivariate_normal(comp.mean, temp_cov)
                        resp = comp.weight * dist.pdf(node.P_sem_proj)
                        sem_resp.append(resp)
                    else:
                        sem_resp.append(0.0)

            # Normalize semantic responsibilities
            total_sem = sum(sem_resp) + 1e-10
            node.semantic_responsibilities = np.array(sem_resp) / total_sem

            # Structural responsibilities
            str_resp = []
            for domain in self.structural_domains:
                for comp in domain:
                    if comp.active:
                        temp_cov = comp.cov * comp.temperature
                        dist = multivariate_normal(comp.mean, temp_cov)
                        resp = comp.weight * dist.pdf(node.P_str_proj)
                        str_resp.append(resp)
                    else:
                        str_resp.append(0.0)

            # Normalize structural responsibilities
            total_str = sum(str_resp) + 1e-10
            node.structural_responsibilities = np.array(str_resp) / total_str

    def _compute_forces(self):
        """Compute forces on nodes from all energy terms (negative gradients)"""
        forces = np.zeros((self.num_nodes, 2))

        # E_sem: Semantic domain forces (responsibility-weighted)
        if self.show_semantic_field:
            for i, node in enumerate(self.nodes):
                resp_idx = 0
                for domain in self.semantic_domains:
                    for comp in domain:
                        if comp.active:
                            resp = node.semantic_responsibilities[resp_idx]
                            temp_cov = comp.cov * comp.temperature

                            # Force towards component mean (negative gradient)
                            precision = np.linalg.inv(temp_cov + np.eye(2)*1e-6)
                            force = resp * precision @ (comp.mean - node.P_sem_proj) * self.alpha
                            forces[i] += force
                        resp_idx += 1

        # E_edges: Typed edge potentials with relation-specific metrics
        if self.show_edge_potentials:
            for edge_type, adj_matrix in self.edges.items():
                metric = self.typed_metrics[edge_type].get_metric()

                for i in range(self.num_nodes):
                    for j in range(self.num_nodes):
                        if adj_matrix[i, j] > 0.1:
                            # Get positions in relation-specific chart (simplified as 2D)
                            pos_i = self.nodes[i].position
                            pos_j = self.nodes[j].position
                            diff = pos_j - pos_i

                            # Compute metric-weighted distance
                            metric_dist_sq = diff.T @ metric @ diff

                            if edge_type in ['SUPPORT', 'ELABORATE', 'COREF']:
                                # Attraction potential: U = w * ||diff||²_M
                                force = adj_matrix[i, j] * 2 * metric @ diff * self.beta
                            elif edge_type == 'CONTRADICT':
                                # Repulsion potential: U = w * exp(-||diff||²_M / σ²)
                                sigma_sq = 1.0
                                exp_term = np.exp(-metric_dist_sq / sigma_sq)
                                force = -adj_matrix[i, j] * 2 * exp_term / sigma_sq * metric @ diff * self.beta
                            else:  # PRECEDE, CAUSE - directional
                                # Directional margin potential
                                u_dir = np.array([1.0, 0.0])  # Simplified direction vector
                                margin = np.dot(u_dir, pos_i) - np.dot(u_dir, pos_j) + 0.5
                                if margin > 0:
                                    force = adj_matrix[i, j] * u_dir * self.beta
                                else:
                                    force = np.zeros(2)

                            forces[i] += force

        # E_struct: Structural domain forces
        if self.show_structural_field:
            for i, node in enumerate(self.nodes):
                resp_idx = 0
                for domain in self.structural_domains:
                    for comp in domain:
                        if comp.active:
                            resp = node.structural_responsibilities[resp_idx]
                            temp_cov = comp.cov * comp.temperature

                            precision = np.linalg.inv(temp_cov + np.eye(2)*1e-6)
                            force = resp * precision @ (comp.mean - node.P_str_proj) * self.gamma
                            forces[i] += force
                        resp_idx += 1

        return forces
    
    def _update_edges(self):
        """Update edge weights with proximal operators (constraint-aware)"""
        edge_change = 0.0

        for edge_type in self.edges:
            old_edges = self.edges[edge_type].copy()

            # Gradient step (simplified)
            gradient = np.random.randn(self.num_nodes, self.num_nodes) * 0.005
            self.edges[edge_type] -= self.eta_A * gradient

            # Proximal operators for constraints
            # 1. Non-negativity
            self.edges[edge_type] = np.maximum(self.edges[edge_type], 0)

            # 2. Sparsity constraint (top-k per row with hysteresis)
            k = 3
            for i in range(self.num_nodes):
                row = self.edges[edge_type][i]
                if np.count_nonzero(row) > k:
                    # Keep top-k values, zero others
                    threshold = np.partition(row, -k)[-k]
                    mask = row < threshold
                    self.edges[edge_type][i][mask] = 0

            # 3. Degree constraints (optional)
            max_degree = 5
            row_sums = np.sum(self.edges[edge_type], axis=1)
            col_sums = np.sum(self.edges[edge_type], axis=0)

            # Project to degree constraints if needed
            for i in range(self.num_nodes):
                if row_sums[i] > max_degree:
                    self.edges[edge_type][i] *= max_degree / row_sums[i]
                if col_sums[i] > max_degree:
                    self.edges[edge_type][:, i] *= max_degree / col_sums[i]

            # 4. Zero diagonal
            np.fill_diagonal(self.edges[edge_type], 0)

            # Track edge changes for convergence
            edge_change += np.linalg.norm(self.edges[edge_type] - old_edges, 'fro')

        self.convergence_metrics['edge_change'].append(edge_change)

    def _compute_energy_breakdown(self):
        """Compute detailed energy breakdown matching the spec"""
        E_sem = 0.0
        E_edges = 0.0
        E_struct = 0.0
        E_rules = 0.0
        E_align = 0.0

        # E_sem: Semantic domain energy (negative log-likelihood)
        for i, node in enumerate(self.nodes):
            for domain in self.semantic_domains:
                domain_likelihood = 0.0
                for comp in domain:
                    if comp.active:
                        temp_cov = comp.cov * comp.temperature
                        dist = multivariate_normal(comp.mean, temp_cov)
                        domain_likelihood += comp.weight * dist.pdf(node.P_sem_proj)
                E_sem -= np.log(domain_likelihood + 1e-10)

        # E_edges: Typed edge potentials
        for edge_type, adj_matrix in self.edges.items():
            metric = self.typed_metrics[edge_type].get_metric()
            for i in range(self.num_nodes):
                for j in range(self.num_nodes):
                    if adj_matrix[i, j] > 0:
                        diff = self.nodes[j].position - self.nodes[i].position
                        metric_dist_sq = diff.T @ metric @ diff

                        if edge_type in ['SUPPORT', 'ELABORATE', 'COREF']:
                            E_edges += adj_matrix[i, j] * metric_dist_sq
                        elif edge_type == 'CONTRADICT':
                            E_edges += adj_matrix[i, j] * np.exp(-metric_dist_sq)
                        else:  # Directional edges
                            u_dir = np.array([1.0, 0.0])
                            margin = np.dot(u_dir, self.nodes[i].position) - np.dot(u_dir, self.nodes[j].position) + 0.5
                            E_edges += adj_matrix[i, j] * max(0, margin)

        # E_struct: Structural domain energy
        for i, node in enumerate(self.nodes):
            for domain in self.structural_domains:
                domain_likelihood = 0.0
                for comp in domain:
                    if comp.active:
                        temp_cov = comp.cov * comp.temperature
                        dist = multivariate_normal(comp.mean, temp_cov)
                        domain_likelihood += comp.weight * dist.pdf(node.P_str_proj)
                E_struct -= np.log(domain_likelihood + 1e-10)

        # E_rules: Structural constraints (sparsity penalties)
        for edge_type, adj_matrix in self.edges.items():
            # L1 sparsity penalty
            E_rules += 0.1 * np.sum(adj_matrix)

            # Degree penalty
            row_sums = np.sum(adj_matrix, axis=1)
            col_sums = np.sum(adj_matrix, axis=0)
            E_rules += 0.01 * (np.sum(np.maximum(row_sums - 5, 0)**2) +
                              np.sum(np.maximum(col_sums - 5, 0)**2))

        # E_align: Distance-edge and responsibility alignment (simplified)
        for edge_type, adj_matrix in self.edges.items():
            for i in range(self.num_nodes):
                for j in range(self.num_nodes):
                    if i != j:
                        # Distance-implied edge likelihood
                        dist = np.linalg.norm(self.nodes[i].position - self.nodes[j].position)
                        p_dist = 1.0 / (1.0 + np.exp(dist - 2.0))  # Sigmoid
                        p_edge = 1.0 / (1.0 + np.exp(-adj_matrix[i, j]))

                        # Binary cross-entropy
                        E_align -= p_edge * np.log(p_dist + 1e-10) + (1-p_edge) * np.log(1-p_dist + 1e-10)

        return {
            'total': self.alpha * E_sem + self.beta * E_edges + self.gamma * E_struct + self.rho * E_rules + (self.zeta_1 + self.zeta_2) * E_align,
            'semantic': E_sem,
            'edges': E_edges,
            'structural': E_struct,
            'rules': E_rules,
            'align': E_align
        }
    
    def _check_topology_changes(self):
        """Check for fission/fusion conditions"""
        # Simplified: just check node distances
        merge_threshold = 0.5
        for i in range(self.num_nodes):
            for j in range(i+1, self.num_nodes):
                dist = np.linalg.norm(self.nodes[i].position - self.nodes[j].position)
                if dist < merge_threshold:
                    # Merge nodes (simplified: just average positions)
                    self.nodes[i].position = (self.nodes[i].position + self.nodes[j].position) / 2
    
    def _animation_step(self, frame):
        """Animation update function"""
        _ = frame  # Suppress unused parameter warning
        self._simulation_step()
        self._update_visualization()
        return []
    
    def _update_visualization(self):
        """Update all visualization components with enhanced displays"""
        # Clear all axes
        self.ax_main.clear()
        self.ax_energy.clear()
        self.ax_charts.clear()
        self.ax_fields.clear()
        self.ax_resp.clear()
        self.ax_tension.clear()
        self.ax_metrics.clear()
        self.ax_concepts.clear()
        self.ax_links.clear()
        self.ax_sem_traj.clear()
        self.ax_confidence.clear()
        self.ax_current_concepts.clear()
        self.ax_transitions.clear()
        self.ax_link_matrix.clear()

        # Draw semantic fields
        if self.show_semantic_field:
            self._draw_semantic_fields()

        # Draw structural fields
        if self.show_structural_field:
            self._draw_structural_fields()

        # Draw nodes with enhanced information and trajectories
        self._draw_nodes()

        # Draw trajectories
        if self.show_trajectories:
            self._draw_trajectories()

        # Draw typed edges
        if self.show_edge_potentials:
            self._draw_edges()

        # Draw energy landscape
        if self.show_energy_landscape:
            self._draw_energy_landscape()

        # Draw chart projections
        if self.show_charts:
            self._draw_chart_projections()

        # Draw node field details
        if self.show_node_fields:
            self._draw_node_fields()

        # Draw responsibilities
        if self.show_responsibilities:
            self._draw_responsibilities()

        # Draw tension and convergence metrics
        if self.show_tensions:
            self._draw_tension_metrics()

        # Draw typed metrics visualization
        self._draw_typed_metrics()

        # Draw concept evolution components
        if self.show_concept_evolution:
            self._draw_concept_evolution()
            self._draw_current_concepts()
            self._draw_concept_transitions()

        # Draw link evolution
        if self.show_link_evolution:
            self._draw_link_evolution()
            self._draw_link_matrix()

        # Draw semantic trajectories
        self._draw_semantic_trajectories()

        # Draw concept confidence
        self._draw_concept_confidence()

        # Update detailed energy history
        if len(self.energy_history['total']) > 0:
            self.ax_history.clear()

            # Plot all energy components
            steps = range(len(self.energy_history['total']))
            self.ax_history.plot(steps, self.energy_history['total'], 'k-', linewidth=2, label='Total')
            self.ax_history.plot(steps, self.energy_history['semantic'], 'r-', alpha=0.7, label='E_sem')
            self.ax_history.plot(steps, self.energy_history['edges'], 'b-', alpha=0.7, label='E_edges')
            self.ax_history.plot(steps, self.energy_history['structural'], 'g-', alpha=0.7, label='E_struct')
            self.ax_history.plot(steps, self.energy_history['rules'], 'm-', alpha=0.7, label='E_rules')
            self.ax_history.plot(steps, self.energy_history['align'], 'c-', alpha=0.7, label='E_align')

            self.ax_history.set_xlabel('Time Step')
            self.ax_history.set_ylabel('Energy')
            self.ax_history.set_title(f'Energy Evolution (Step {self.time_step})')
            self.ax_history.legend(loc='upper right', fontsize=8)
            self.ax_history.grid(True, alpha=0.3)

        # Set main plot properties
        self.ax_main.set_xlim(-6, 6)
        self.ax_main.set_ylim(-6, 6)
        self.ax_main.set_aspect('equal')
        self.ax_main.set_title(f'Graph-Manifold State (t={self.time_step})', fontsize=14)
        self.ax_main.grid(True, alpha=0.2)

        plt.draw()
    
    def _draw_semantic_fields(self):
        """Draw semantic domain GMM components"""
        x = np.linspace(-6, 6, 100)
        y = np.linspace(-6, 6, 100)
        X, Y = np.meshgrid(x, y)
        pos = np.dstack((X, Y))
        
        for domain_idx, domain in enumerate(self.semantic_domains):
            Z = np.zeros_like(X)
            for comp in domain:
                rv = multivariate_normal(comp.mean, comp.cov * self.slider_temp.val)
                Z += comp.weight * rv.pdf(pos)
            
            # Draw contours
            self.ax_main.contour(X, Y, Z, levels=3,
                               colors=domain[0].color, alpha=0.3)
            
            # Label domain
            self.ax_main.text(domain[0].mean[0], domain[0].mean[1], 
                            f'Sem{domain_idx}', fontsize=8, 
                            color=domain[0].color, weight='bold')
    
    def _draw_structural_fields(self):
        """Draw structural domain components"""
        for domain in self.structural_domains:
            for comp in domain:
                circle = Circle(comp.mean, np.sqrt(comp.cov[0,0]) * self.slider_temp.val,
                              fill=False, linestyle='--',
                              color=comp.color, alpha=0.5, linewidth=2)
                self.ax_main.add_patch(circle)
    
    def _draw_nodes(self):
        """Draw nodes with enhanced concept information"""
        for node in self.nodes:
            # Get current concept
            current_concept = node.concept_history[-1] if node.concept_history else None

            # Node circle with concept-based coloring
            if current_concept:
                # Color based on concept confidence
                confidence = current_concept.confidence
                color_intensity = confidence
                facecolor = plt.cm.viridis(color_intensity)
            else:
                facecolor = 'lightblue'

            circle = Circle(node.position, 0.3, facecolor=facecolor,
                          edgecolor='darkblue', linewidth=2, zorder=5)
            self.ax_main.add_patch(circle)

            # Node ID and concept label
            if current_concept:
                label = f"{node.id}\n{current_concept.concept_label[:8]}"
            else:
                label = str(node.id)

            self.ax_main.text(node.position[0], node.position[1],
                            label, ha='center', va='center',
                            fontsize=8, weight='bold', zorder=6)

            # Role indicator with discourse role
            role_angle = np.argmax(node.V_role[:4]) * np.pi / 2
            role_color = 'green'
            if current_concept:
                if 'evidence' in current_concept.discourse_role:
                    role_color = 'blue'
                elif 'conclusion' in current_concept.discourse_role:
                    role_color = 'red'
                elif 'premise' in current_concept.discourse_role:
                    role_color = 'orange'

            role_indicator = FancyArrowPatch(
                node.position,
                node.position + 0.4 * np.array([np.cos(role_angle), np.sin(role_angle)]),
                arrowstyle='->', mutation_scale=10, color=role_color, linewidth=1.5
            )
            self.ax_main.add_patch(role_indicator)

            # Uncertainty visualization
            uncert_size = np.exp(node.V_uncert[0]) * 0.1
            uncert_circle = Circle(node.position, 0.3 + uncert_size,
                                 fill=False, linestyle=':',
                                 color='red', alpha=0.5)
            self.ax_main.add_patch(uncert_circle)

            # KB link indicators
            if current_concept and current_concept.kb_links:
                for i, link in enumerate(current_concept.kb_links[:3]):  # Show max 3 links
                    link_angle = (i * 2 * np.pi / 3) + np.pi/4
                    link_pos = node.position + 0.6 * np.array([np.cos(link_angle), np.sin(link_angle)])
                    self.ax_main.text(link_pos[0], link_pos[1], link[:4],
                                    fontsize=6, ha='center', va='center',
                                    bbox=dict(boxstyle="round,pad=0.1", facecolor='yellow', alpha=0.7))

    def _draw_trajectories(self):
        """Draw node movement trajectories"""
        for node in self.nodes:
            if len(node.trajectory) > 1:
                trajectory = np.array(node.trajectory)

                # Draw trajectory line with fading alpha
                for i in range(len(trajectory) - 1):
                    alpha = (i + 1) / len(trajectory) * 0.7
                    self.ax_main.plot([trajectory[i][0], trajectory[i+1][0]],
                                    [trajectory[i][1], trajectory[i+1][1]],
                                    'gray', alpha=alpha, linewidth=1, zorder=1)

                # Mark significant concept changes
                concept_changes = []
                if len(node.concept_history) > 1:
                    for i in range(1, len(node.concept_history)):
                        if i < len(node.trajectory):
                            concept_changes.append(node.trajectory[i])

                if concept_changes:
                    changes_array = np.array(concept_changes)
                    self.ax_main.scatter(changes_array[:, 0], changes_array[:, 1],
                                       c='red', s=20, marker='x', zorder=4, alpha=0.8)

    def _draw_concept_evolution(self):
        """Draw concept evolution timeline"""
        if not self.nodes or not self.nodes[0].concept_history:
            return

        # Select a representative node for detailed concept tracking
        node = self.nodes[0]
        concepts = node.concept_history

        if len(concepts) < 2:
            return

        # Plot concept labels over time
        times = list(range(len(concepts)))
        concept_labels = [c.concept_label for c in concepts]
        confidences = [c.confidence for c in concepts]

        # Create timeline
        self.ax_concepts.scatter(times, [0] * len(times), c=confidences,
                               cmap='viridis', s=50, alpha=0.8)

        # Add concept labels
        for i, (time, label, conf) in enumerate(zip(times, concept_labels, confidences)):
            self.ax_concepts.annotate(label[:8], (time, 0),
                                    xytext=(0, 10 + (i % 3) * 15),
                                    textcoords='offset points',
                                    fontsize=8, ha='center',
                                    bbox=dict(boxstyle="round,pad=0.2",
                                            facecolor=plt.cm.viridis(conf), alpha=0.7))

        self.ax_concepts.set_xlabel('Time Steps')
        self.ax_concepts.set_title(f'Node {node.id} Concept Evolution')
        self.ax_concepts.set_ylim(-0.5, 0.5)
        self.ax_concepts.grid(True, alpha=0.3)

    def _draw_link_evolution(self):
        """Draw knowledge base link evolution"""
        if not self.nodes:
            return

        # Collect all unique links across all nodes
        all_links = set()
        for node in self.nodes:
            for link_name in node.link_evolution.keys():
                all_links.add(link_name)

        if not all_links:
            return

        # Plot link strength evolution for each link
        colors = plt.cm.tab10(np.linspace(0, 1, len(all_links)))

        for i, link in enumerate(all_links):
            link_data = []
            times = []

            # Collect data across all nodes
            for node in self.nodes:
                if link in node.link_evolution:
                    node_times = list(range(len(node.link_evolution[link])))
                    link_data.extend(node.link_evolution[link])
                    times.extend([t + node.id * 100 for t in node_times])  # Offset by node ID

            if link_data:
                self.ax_links.plot(times, link_data, color=colors[i],
                                 label=link[:10], alpha=0.7, linewidth=2)

        self.ax_links.set_xlabel('Time Steps')
        self.ax_links.set_ylabel('Link Strength')
        self.ax_links.legend(fontsize=8, loc='upper right')
        self.ax_links.grid(True, alpha=0.3)

    def _draw_semantic_trajectories(self):
        """Draw semantic embedding trajectories in PCA space"""
        if not self.nodes or len(self.nodes[0].semantic_trajectory) < 2:
            return

        # Collect all semantic embeddings
        all_embeddings = []
        node_indices = []

        for node_idx, node in enumerate(self.nodes):
            for emb in node.semantic_trajectory:
                all_embeddings.append(emb)
                node_indices.append(node_idx)

        if len(all_embeddings) < 2:
            return

        # Apply PCA for 2D visualization
        if SKLEARN_AVAILABLE:
            pca = PCA(n_components=2)
            embeddings_2d = pca.fit_transform(all_embeddings)
            var_explained = pca.explained_variance_ratio_
        else:
            # Simple 2D projection using first two dimensions
            embeddings_2d = np.array(all_embeddings)[:, :2]
            var_explained = [0.5, 0.3]  # Dummy values

        # Plot trajectories for each node
        colors = plt.cm.tab10(np.linspace(0, 1, len(self.nodes)))

        start_idx = 0
        for node_idx, node in enumerate(self.nodes):
            end_idx = start_idx + len(node.semantic_trajectory)
            node_traj = embeddings_2d[start_idx:end_idx]

            if len(node_traj) > 1:
                self.ax_sem_traj.plot(node_traj[:, 0], node_traj[:, 1],
                                    color=colors[node_idx], alpha=0.7,
                                    linewidth=2, label=f'Node {node.id}')

                # Mark start and end
                self.ax_sem_traj.scatter(node_traj[0, 0], node_traj[0, 1],
                                       color=colors[node_idx], s=50, marker='o')
                self.ax_sem_traj.scatter(node_traj[-1, 0], node_traj[-1, 1],
                                       color=colors[node_idx], s=50, marker='s')

            start_idx = end_idx

        self.ax_sem_traj.set_xlabel(f'PC1 ({var_explained[0]:.2%} var)')
        self.ax_sem_traj.set_ylabel(f'PC2 ({var_explained[1]:.2%} var)')
        self.ax_sem_traj.legend(fontsize=8)
        self.ax_sem_traj.grid(True, alpha=0.3)

    def _draw_concept_confidence(self):
        """Draw concept confidence evolution"""
        if not self.nodes:
            return

        # Plot confidence over time for each node
        colors = plt.cm.tab10(np.linspace(0, 1, len(self.nodes)))

        for node_idx, node in enumerate(self.nodes):
            if node.concept_history:
                confidences = [c.confidence for c in node.concept_history]
                times = list(range(len(confidences)))

                self.ax_confidence.plot(times, confidences, color=colors[node_idx],
                                      alpha=0.7, linewidth=2, label=f'Node {node.id}')

        self.ax_confidence.set_xlabel('Concept Changes')
        self.ax_confidence.set_ylabel('Confidence')
        self.ax_confidence.set_ylim(0, 1)
        self.ax_confidence.legend(fontsize=8)
        self.ax_confidence.grid(True, alpha=0.3)

    def _draw_current_concepts(self):
        """Draw current concept states as text"""
        self.ax_current_concepts.clear()
        self.ax_current_concepts.axis('off')

        if not self.nodes:
            return

        y_pos = 0.95
        for node in self.nodes:
            if node.concept_history:
                current_concept = node.concept_history[-1]

                # Node header
                self.ax_current_concepts.text(0.05, y_pos, f'Node {node.id}:',
                                            fontsize=12, weight='bold',
                                            transform=self.ax_current_concepts.transAxes)
                y_pos -= 0.05

                # Concept details
                details = [
                    f"Concept: {current_concept.concept_label}",
                    f"Role: {current_concept.discourse_role}",
                    f"Modality: {current_concept.modality}",
                    f"Temporal: {current_concept.temporal_aspect}",
                    f"Confidence: {current_concept.confidence:.3f}",
                    f"KB Links: {', '.join(current_concept.kb_links[:3])}"
                ]

                for detail in details:
                    self.ax_current_concepts.text(0.1, y_pos, detail, fontsize=9,
                                                transform=self.ax_current_concepts.transAxes)
                    y_pos -= 0.03

                y_pos -= 0.02  # Extra space between nodes

                if y_pos < 0.1:  # Prevent overflow
                    break

    def _draw_concept_transitions(self):
        """Draw concept transition patterns"""
        if not self.nodes:
            return

        # Collect all concept transitions
        transitions = {}

        for node in self.nodes:
            if len(node.concept_history) > 1:
                for i in range(len(node.concept_history) - 1):
                    from_concept = node.concept_history[i].concept_label
                    to_concept = node.concept_history[i + 1].concept_label

                    transition = (from_concept, to_concept)
                    transitions[transition] = transitions.get(transition, 0) + 1

        if not transitions:
            return

        # Create transition matrix visualization
        unique_concepts = list(set([c for trans in transitions.keys() for c in trans]))
        n_concepts = len(unique_concepts)

        if n_concepts > 1:
            transition_matrix = np.zeros((n_concepts, n_concepts))

            for (from_c, to_c), count in transitions.items():
                from_idx = unique_concepts.index(from_c)
                to_idx = unique_concepts.index(to_c)
                transition_matrix[from_idx, to_idx] = count

            # Plot heatmap
            im = self.ax_transitions.imshow(transition_matrix, cmap='Blues', aspect='auto')

            # Add labels
            self.ax_transitions.set_xticks(range(n_concepts))
            self.ax_transitions.set_yticks(range(n_concepts))
            self.ax_transitions.set_xticklabels([c[:8] for c in unique_concepts], rotation=45, fontsize=8)
            self.ax_transitions.set_yticklabels([c[:8] for c in unique_concepts], fontsize=8)

            # Add colorbar
            plt.colorbar(im, ax=self.ax_transitions, fraction=0.046, pad=0.04)

            self.ax_transitions.set_xlabel('To Concept')
            self.ax_transitions.set_ylabel('From Concept')

    def _draw_link_matrix(self):
        """Draw KB link strength matrix"""
        if not self.nodes:
            return

        # Collect current link strengths for all nodes
        all_links = set()
        for node in self.nodes:
            if node.concept_history:
                current_concept = node.concept_history[-1]
                all_links.update(current_concept.kb_links)

        if not all_links:
            return

        all_links = list(all_links)
        n_nodes = len(self.nodes)
        n_links = len(all_links)

        # Create link strength matrix
        link_matrix = np.zeros((n_nodes, n_links))

        for i, node in enumerate(self.nodes):
            if node.concept_history:
                current_concept = node.concept_history[-1]
                for j, link in enumerate(all_links):
                    if link in current_concept.kb_links:
                        # Use confidence as link strength
                        link_matrix[i, j] = current_concept.confidence

        # Plot heatmap
        im = self.ax_link_matrix.imshow(link_matrix, cmap='Reds', aspect='auto')

        # Add labels
        self.ax_link_matrix.set_xticks(range(n_links))
        self.ax_link_matrix.set_yticks(range(n_nodes))
        self.ax_link_matrix.set_xticklabels([link[:8] for link in all_links], rotation=45, fontsize=8)
        self.ax_link_matrix.set_yticklabels([f'Node {i}' for i in range(n_nodes)], fontsize=8)

        # Add colorbar
        plt.colorbar(im, ax=self.ax_link_matrix, fraction=0.046, pad=0.04)

        self.ax_link_matrix.set_xlabel('KB Links')
        self.ax_link_matrix.set_ylabel('Nodes')
    
    def _draw_edges(self):
        """Draw typed edges with appropriate styles"""
        edge_styles = {
            'SUPPORT': {'color': 'green', 'style': '-', 'arrow': '->'},
            'ELABORATE': {'color': 'blue', 'style': '--', 'arrow': '->'},
            'CONTRADICT': {'color': 'red', 'style': '-', 'arrow': '<->'},
            'PRECEDE': {'color': 'orange', 'style': '-', 'arrow': '->'},
            'CAUSE': {'color': 'purple', 'style': '-', 'arrow': '-|>'}  # Changed from '=>' to '-|>'
        }
        
        for edge_type, adj_matrix in self.edges.items():
            style = edge_styles.get(edge_type, {'color': 'gray', 'style': '-', 'arrow': '->'})
            
            for i in range(self.num_nodes):
                for j in range(self.num_nodes):
                    if adj_matrix[i, j] > 0.1:  # Threshold for visualization
                        start = self.nodes[i].position
                        end = self.nodes[j].position
                        
                        # Offset for multiple edges
                        offset = 0.1 * (hash((i, j, edge_type)) % 3 - 1)
                        
                        arrow = FancyArrowPatch(
                            start, end,
                            connectionstyle=f"arc3,rad={offset}",
                            arrowstyle=style['arrow'],
                            color=style['color'],
                            alpha=min(adj_matrix[i, j], 0.8),
                            linewidth=1 + adj_matrix[i, j] * 2,
                            linestyle=style['style'],
                            zorder=1
                        )
                        self.ax_main.add_patch(arrow)
    
    def _draw_energy_landscape(self):
        """Draw 3D energy landscape"""
        # Create grid
        x = np.linspace(-6, 6, 50)
        y = np.linspace(-6, 6, 50)
        X, Y = np.meshgrid(x, y)
        Z = np.zeros_like(X)
        
        # Compute energy at each point
        for i in range(X.shape[0]):
            for j in range(X.shape[1]):
                pos = np.array([X[i, j], Y[i, j]])
                
                # Semantic energy
                for domain in self.semantic_domains:
                    for comp in domain:
                        dist = multivariate_normal(comp.mean, comp.cov)
                        Z[i, j] -= comp.weight * np.log(dist.pdf(pos) + 1e-10)
        
        # Plot surface
        self.ax_energy.plot_surface(X, Y, Z, cmap='viridis', alpha=0.6)
        
        # Plot node positions
        for node in self.nodes:
            self.ax_energy.scatter(node.position[0], node.position[1], 0,
                                 color='red', s=50, marker='o')
        
        self.ax_energy.set_xlabel('X')
        self.ax_energy.set_ylabel('Y')
        self.ax_energy.set_zlabel('Energy')
        self.ax_energy.set_title('Energy Landscape')
    
    def _draw_chart_projections(self):
        """Visualize different chart projections"""
        # Create subplots for different charts
        charts = ['P_sem', 'P_role', 'P_val']
        colors = ['blue', 'green', 'red']
        
        for chart, color in zip(charts, colors):
            # Simplified: just show node positions with different transformations
            transformed_positions = []
            for node in self.nodes:
                if chart == 'P_sem':
                    pos = node.position
                elif chart == 'P_role':
                    # Rotate based on role
                    angle = np.sum(node.V_role[:2])
                    rot = np.array([[np.cos(angle), -np.sin(angle)],
                                   [np.sin(angle), np.cos(angle)]])
                    pos = rot @ node.position
                else:  # P_val
                    # Scale based on valence
                    scale = 1 + 0.5 * np.tanh(node.V_mod[0])
                    pos = node.position * scale
                
                transformed_positions.append(pos)
                
            # Plot
            positions = np.array(transformed_positions)
            self.ax_charts.scatter(positions[:, 0], positions[:, 1], 
                                 c=color, alpha=0.6, label=chart, s=30)
        
        self.ax_charts.legend()
        self.ax_charts.set_xlabel('Dim 1')
        self.ax_charts.set_ylabel('Dim 2')
        self.ax_charts.grid(True, alpha=0.3)
    
    def _draw_node_fields(self):
        """Visualize node field details"""
        if len(self.nodes) > 0:
            # Select first node for detailed view
            node = self.nodes[0]
            
            fields = ['V_sem', 'V_role', 'V_mod', 'V_time', 'V_uncert', 'V_type']
            field_data = [
                node.V_sem[:10],  # First 10 dims
                node.V_role[:8],
                node.V_mod[:8],
                node.V_time[:8],
                node.V_uncert,
                node.V_type[:8]
            ]
            
            y_pos = 0.9
            for field, data in zip(fields, field_data):
                # Create mini heatmap
                self.ax_fields.text(0.05, y_pos, field + ':', fontsize=10, 
                                  transform=self.ax_fields.transAxes)
                
                # Plot values as bars
                bar_height = 0.08
                self.ax_fields.barh(y_pos - bar_height/2, np.abs(data).sum(),
                                  height=bar_height,
                                  transform=self.ax_fields.transAxes)
                
                y_pos -= 0.15
            
            self.ax_fields.set_xlim(0, 1)
            self.ax_fields.set_ylim(0, 1)
            self.ax_fields.axis('off')
            self.ax_fields.set_title(f'Node {node.id} Fields')

    def _draw_responsibilities(self):
        """Visualize domain responsibilities for nodes"""
        if len(self.nodes) == 0:
            return

        # Create heatmap of responsibilities
        sem_resp_matrix = np.array([node.semantic_responsibilities for node in self.nodes])

        # Plot semantic responsibilities
        im1 = self.ax_resp.imshow(sem_resp_matrix.T, aspect='auto', cmap='Reds', alpha=0.7)
        self.ax_resp.set_xlabel('Nodes')
        self.ax_resp.set_ylabel('Semantic Components')
        self.ax_resp.set_title('Semantic Responsibilities γ_sem')

        # Add colorbar
        plt.colorbar(im1, ax=self.ax_resp, fraction=0.046, pad=0.04)

    def _draw_tension_metrics(self):
        """Visualize tension and convergence metrics"""
        if len(self.tension_history) > 0:
            steps = range(len(self.tension_history))
            self.ax_tension.plot(steps, self.tension_history, 'r-', linewidth=2, label='Avg Tension')

        if len(self.convergence_metrics['node_change']) > 0:
            steps = range(len(self.convergence_metrics['node_change']))
            self.ax_tension.plot(steps, self.convergence_metrics['node_change'], 'b-',
                               alpha=0.7, label='Node Change')

        if len(self.convergence_metrics['edge_change']) > 0:
            steps = range(len(self.convergence_metrics['edge_change']))
            self.ax_tension.plot(steps, self.convergence_metrics['edge_change'], 'g-',
                               alpha=0.7, label='Edge Change')

        self.ax_tension.set_xlabel('Time Step')
        self.ax_tension.set_ylabel('Magnitude')
        self.ax_tension.legend(fontsize=8)
        self.ax_tension.grid(True, alpha=0.3)

        # Add fission threshold line
        if len(self.tension_history) > 10:
            tension_p95 = np.percentile(self.tension_history, 95)
            self.ax_tension.axhline(y=tension_p95, color='red', linestyle='--',
                                  alpha=0.5, label='Fission Threshold')

    def _draw_typed_metrics(self):
        """Visualize typed metrics M_τ"""
        edge_types = list(self.typed_metrics.keys())
        n_types = len(edge_types)

        if n_types == 0:
            return

        # Create a grid showing metric eigenvalues
        eigenvals_data = []
        for edge_type in edge_types:
            metric = self.typed_metrics[edge_type].get_metric()
            eigenvals = np.linalg.eigvals(metric)
            eigenvals_data.append(eigenvals)

        eigenvals_matrix = np.array(eigenvals_data)

        # Plot as heatmap
        im = self.ax_metrics.imshow(eigenvals_matrix, aspect='auto', cmap='viridis')
        self.ax_metrics.set_xticks(range(len(eigenvals_matrix[0])))
        self.ax_metrics.set_xticklabels([f'λ_{i+1}' for i in range(len(eigenvals_matrix[0]))])
        self.ax_metrics.set_yticks(range(n_types))
        self.ax_metrics.set_yticklabels(edge_types, fontsize=8)
        self.ax_metrics.set_xlabel('Eigenvalues')
        self.ax_metrics.set_ylabel('Edge Types')

        # Add colorbar
        plt.colorbar(im, ax=self.ax_metrics, fraction=0.046, pad=0.04)

        # Add text annotations
        for i in range(n_types):
            for j in range(len(eigenvals_matrix[i])):
                text = f'{eigenvals_matrix[i, j]:.2f}'
                self.ax_metrics.text(j, i, text, ha='center', va='center',
                                   color='white' if eigenvals_matrix[i, j] > eigenvals_matrix.mean() else 'black',
                                   fontsize=8)

    def run(self):
        """Start the enhanced visualization"""
        plt.show()

if __name__ == "__main__":
    # Create enhanced visualizer with concept evolution tracking
    visualizer = MinervaVisualizer(num_nodes=8, seed=42)
    print("Minerva 0.17 Graph-Manifold Visualizer with Concept Evolution")
    print("=" * 65)
    print("Core Architecture Features:")
    print("- Factorized node fields (V_sem, V_role, V_mod, V_time, V_uncert, V_type)")
    print("- Typed edge weights A_τ with relation-specific metrics M_τ")
    print("- Semantic & Structural GMM domain families")
    print("- Chart projections (P_sem, P_val, P_str, P_τ)")
    print("- Alternating dynamics with proximal edge updates")
    print("- Energy breakdown (E_sem + E_edges + E_struct + E_rules + E_align)")
    print("- Responsibility-weighted forces and tension monitoring")
    print()
    print("NEW: Concept Evolution Features:")
    print("- Real-time concept interpretation from node states")
    print("- Concept confidence tracking and evolution")
    print("- Knowledge base link evolution")
    print("- Semantic trajectory visualization in PCA space")
    print("- Concept transition pattern analysis")
    print("- Discourse role and modality tracking")
    print("- Node movement trajectories with concept change markers")
    print()
    print("Visualization Panels:")
    print("- Main: Graph-Manifold with concept trajectories")
    print("- Concept Evolution: Timeline of concept changes")
    print("- Link Evolution: KB link strength over time")
    print("- Semantic Trajectories: PCA of embedding evolution")
    print("- Current Concepts: Detailed concept interpretations")
    print("- Transition Matrix: Concept change patterns")
    print("- Link Matrix: Current KB link strengths")
    print("- Confidence: Concept confidence evolution")
    print()
    print("Interactive Controls:")
    print("- Use checkboxes to toggle visualization components")
    print("- Adjust sliders to modify energy weights and learning rates")
    print("- Click 'Play' to start simulation or 'Step' for single steps")
    print("- Click 'Track Concepts' to toggle concept tracking modes")
    print("- Click 'Reset System' to reinitialize")
    print()
    print("Watch how concepts evolve as nodes move through the manifold!")
    print("Concept changes are marked with red X's on trajectories.")
    visualizer.run()
