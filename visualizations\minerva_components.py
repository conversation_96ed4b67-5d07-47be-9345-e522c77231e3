import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, FancyBboxPatch, Circle, Polygon
from matplotlib.collections import PatchCollection
import matplotlib.cm as cm
from typing import List, Tuple, Dict
import networkx as nx

class ArchitectureDiagram:
    """Visualize the three-phase Minerva architecture"""
    
    def __init__(self):
        self.fig, self.ax = plt.subplots(1, 1, figsize=(16, 10))
        self.ax.set_xlim(0, 10)
        self.ax.set_ylim(0, 10)
        self.ax.axis('off')
        
    def draw_phase1_planner(self):
        """Draw Phase 1: Graph-Manifold Seeding components"""
        # Planner box
        planner = FancyBboxPatch((0.5, 7), 2, 1.5, 
                                boxstyle="round,pad=0.1",
                                facecolor='lightblue',
                                edgecolor='darkblue',
                                linewidth=2)
        self.ax.add_patch(planner)
        self.ax.text(1.5, 7.75, 'Planner Module', ha='center', va='center', 
                    fontsize=12, weight='bold')
        
        # Input prompt
        self.ax.arrow(0.2, 7.75, 0.3, 0, head_width=0.1, head_length=0.05, 
                     fc='black', ec='black')
        self.ax.text(0.1, 7.75, 'Prompt', ha='right', va='center')
        
        # Factorized fields
        fields = ['V_sem', 'V_role', 'V_mod', 'V_time', 'V_uncert', 'V_type', 'V_link']
        colors = ['red', 'green', 'blue', 'orange', 'purple', 'cyan', 'magenta']
        
        for i, (field, color) in enumerate(zip(fields, colors)):
            y_pos = 6.5 - i * 0.3
            field_box = Rectangle((3, y_pos - 0.1), 0.8, 0.2, 
                                facecolor=color, alpha=0.3)
            self.ax.add_patch(field_box)
            self.ax.text(3.4, y_pos, field, ha='center', va='center', fontsize=9)
            
            # Arrow from planner
            self.ax.arrow(2.5, 7.5, 0.4, y_pos - 7.5, 
                         head_width=0.05, head_length=0.03,
                         fc='gray', ec='gray', alpha=0.5)
        
        # Typed charts
        self.ax.text(5, 7.5, 'Typed Charts', fontsize=11, weight='bold')
        charts = ['P_sem', 'P_τ', 'P_val', 'P_str']
        for i, chart in enumerate(charts):
            chart_box = FancyBboxPatch((4.5, 6.8 - i * 0.4), 1, 0.3,
                                     boxstyle="round,pad=0.05",
                                     facecolor='lightyellow',
                                     edgecolor='goldenrod')
            self.ax.add_patch(chart_box)
            self.ax.text(5, 6.95 - i * 0.4, chart, ha='center', va='center', 
                        fontsize=9)
        
        # Edge initialization
        edge_box = FancyBboxPatch((6, 6.5), 1.5, 1,
                                boxstyle="round,pad=0.1",
                                facecolor='lightgreen',
                                edgecolor='darkgreen',
                                linewidth=2)
        self.ax.add_patch(edge_box)
        self.ax.text(6.75, 7, 'Typed Edges\nA_τ', ha='center', va='center',
                    fontsize=10, weight='bold')
        
        # Initialization energy
        energy_box = FancyBboxPatch((4, 3.5), 2, 0.8,
                                  boxstyle="round,pad=0.1",
                                  facecolor='lightyellow',
                                  edgecolor='orange',
                                  linewidth=2)
        self.ax.add_patch(energy_box)
        self.ax.text(5, 3.9, 'E_init = α E_sem + β E_edges + γ E_rules',
                    ha='center', va='center', fontsize=9)
        
        # Arrow to Phase 2
        self.ax.arrow(5, 3.3, 0, -0.5, head_width=0.2, head_length=0.1,
                     fc='black', ec='black', linewidth=2)
        self.ax.text(5.2, 2.5, 'Initial Graph-Manifold State', 
                    ha='left', va='center', fontsize=10, style='italic')
    
    def draw_phase2_simulation(self):
        """Draw Phase 2: Graph-Manifold Simulation components"""
        # Main simulation box
        sim_box = FancyBboxPatch((0.5, 0.5), 9, 2,
                               boxstyle="round,pad=0.1",
                               facecolor='lavender',
                               edgecolor='purple',
                               linewidth=3)
        self.ax.add_patch(sim_box)
        self.ax.text(5, 2.2, 'Graph-Manifold Simulation', 
                    ha='center', va='top', fontsize=14, weight='bold')
        
        # Semantic domains
        sem_box = FancyBboxPatch((1, 1), 1.5, 0.8,
                               boxstyle="round,pad=0.05",
                               facecolor='lightcoral',
                               edgecolor='darkred')
        self.ax.add_patch(sem_box)
        self.ax.text(1.75, 1.4, 'Semantic\nDomains\n(GMM)', 
                    ha='center', va='center', fontsize=9)
        
        # Structural domains
        struct_box = FancyBboxPatch((2.8, 1), 1.5, 0.8,
                                  boxstyle="round,pad=0.05",
                                  facecolor='lightsteelblue',
                                  edgecolor='steelblue')
        self.ax.add_patch(struct_box)
        self.ax.text(3.55, 1.4, 'Structural\nDomains\n(GMM)', 
                    ha='center', va='center', fontsize=9)
        
        # Node dynamics
        node_box = FancyBboxPatch((4.6, 1), 1.5, 0.8,
                                boxstyle="round,pad=0.05",
                                facecolor='lightgreen',
                                edgecolor='darkgreen')
        self.ax.add_patch(node_box)
        self.ax.text(5.35, 1.4, 'Node\nDynamics\n(Gradient)', 
                    ha='center', va='center', fontsize=9)
        
        # Edge dynamics
        edge_box = FancyBboxPatch((6.4, 1), 1.5, 0.8,
                                boxstyle="round,pad=0.05",
                                facecolor='lightyellow',
                                edgecolor='goldenrod')
        self.ax.add_patch(edge_box)
        self.ax.text(7.15, 1.4, 'Edge\nDynamics\n(Proximal)', 
                    ha='center', va='center', fontsize=9)
        
        # Alternating optimization arrows
        self.ax.annotate('', xy=(6.4, 1.4), xytext=(6.1, 1.4),
                        arrowprops=dict(arrowstyle='<->', lw=2))
        
        # Fission/Fusion
        fission_box = FancyBboxPatch((8.2, 1), 1, 0.8,
                                   boxstyle="round,pad=0.05",
                                   facecolor='pink',
                                   edgecolor='crimson')
        self.ax.add_patch(fission_box)
        self.ax.text(8.7, 1.4, 'Fission/\nFusion', 
                    ha='center', va='center', fontsize=9)
    
    def draw_phase3_interpretation(self):
        """Draw Phase 3: Interpretation components"""
        # Create vertical layout for Phase 3
        y_start = 4.5
        
        # Extraction box
        extract_box = FancyBboxPatch((7.5, y_start), 2, 0.6,
                                   boxstyle="round,pad=0.05",
                                   facecolor='lightcyan',
                                   edgecolor='darkcyan',
                                   linewidth=2)
        self.ax.add_patch(extract_box)
        self.ax.text(8.5, y_start + 0.3, 'Extraction Interface',
                    ha='center', va='center', fontsize=10, weight='bold')
        
        # ConceptRecords
        concept_box = FancyBboxPatch((7.5, y_start + 0.8), 2, 0.5,
                                   boxstyle="round,pad=0.05",
                                   facecolor='lightyellow',
                                   edgecolor='orange')
        self.ax.add_patch(concept_box)
        self.ax.text(8.5, y_start + 1.05, 'ConceptRecords',
                    ha='center', va='center', fontsize=9)
        
        # Discourse scaffold
        scaffold_box = FancyBboxPatch((7.5, y_start + 1.4), 2, 0.5,
                                    boxstyle="round,pad=0.05",
                                    facecolor='lightgreen',
                                    edgecolor='darkgreen')
        self.ax.add_patch(scaffold_box)
        self.ax.text(8.5, y_start + 1.65, 'Discourse Scaffold',
                    ha='center', va='center', fontsize=9)
        
        # Probabilistic interpreter
        interp_box = FancyBboxPatch((7.5, y_start + 2), 2, 0.6,
                                  boxstyle="round,pad=0.05",
                                  facecolor='lavender',
                                  edgecolor='purple',
                                  linewidth=2)
        self.ax.add_patch(interp_box)
        self.ax.text(8.5, y_start + 2.3, 'Probabilistic\nInterpreter',
                    ha='center', va='center', fontsize=10, weight='bold')
        
        # Arrows
        self.ax.arrow(5, 2.5, 2.3, y_start + 0.3 - 2.5,
                     head_width=0.1, head_length=0.05,
                     fc='black', ec='black')
        
        # Output
        self.ax.arrow(8.5, y_start + 2.6, 0, 0.3,
                     head_width=0.1, head_length=0.05,
                     fc='black', ec='black')
        self.ax.text(8.5, y_start + 3.1, 'Generated Text',
                    ha='center', va='top', fontsize=10, style='italic')
    
    def draw_complete_architecture(self):
        """Draw the complete Minerva architecture"""
        self.ax.set_title('Minerva 0.17: Graph-Manifold Architecture',
                         fontsize=16, weight='bold', pad=20)
        
        self.draw_phase1_planner()
        self.draw_phase2_simulation()
        self.draw_phase3_interpretation()
        
        # Phase labels
        self.ax.text(1, 9.5, 'Phase 1: Planning', fontsize=14, weight='bold')
        self.ax.text(1, 2.8, 'Phase 2: Simulation', fontsize=14, weight='bold')
        self.ax.text(7.5, 7.5, 'Phase 3: Interpretation', fontsize=14, weight='bold')
        
        plt.tight_layout()
        return self.fig

class EnergyLandscapeVisualizer:
    """Specialized visualizer for energy landscapes and optimization paths"""
    
    def __init__(self):
        self.fig = plt.figure(figsize=(15, 10))
        
    def create_energy_surface(self, num_domains=3):
        """Create a complex energy surface with multiple domains"""
        x = np.linspace(-5, 5, 200)
        y = np.linspace(-5, 5, 200)
        X, Y = np.meshgrid(x, y)
        Z = np.zeros_like(X)
        
        # Add semantic domain components
        domain_centers = [(0, 0), (2, 2), (-2, 1), (1, -2), (-1, -1)]
        domain_strengths = [1.0, 0.8, 0.7, 0.9, 0.6]
        
        for center, strength in zip(domain_centers[:num_domains], 
                                   domain_strengths[:num_domains]):
            # Gaussian wells
            Z -= strength * np.exp(-((X - center[0])**2 + (Y - center[1])**2) / 2)
            
            # Add some noise/roughness
            Z += 0.1 * strength * np.sin(2 * (X - center[0])) * np.cos(2 * (Y - center[1]))
        
        # Add edge potential ridges
        Z += 0.3 * np.exp(-((X - Y)**2) / 4)  # Diagonal ridge
        Z += 0.2 * np.exp(-((X + Y - 1)**2) / 4)  # Off-diagonal ridge
        
        return X, Y, Z
    
    def plot_optimization_trajectory(self, ax, X, Y, Z, num_steps=50):
        """Plot optimization trajectory on energy surface"""
        # Random initial position
        pos = np.array([np.random.uniform(-3, 3), np.random.uniform(-3, 3)])
        trajectory = [pos.copy()]
        
        # Simulate gradient descent with momentum
        velocity = np.zeros(2)
        momentum = 0.9
        lr = 0.1
        
        for step in range(num_steps):
            # Compute gradient numerically
            i = np.argmin(np.abs(X[0, :] - pos[0]))
            j = np.argmin(np.abs(Y[:, 0] - pos[1]))
            
            if 0 < i < X.shape[1]-1 and 0 < j < Y.shape[0]-1:
                grad_x = (Z[j, i+1] - Z[j, i-1]) / (X[0, i+1] - X[0, i-1])
                grad_y = (Z[j+1, i] - Z[j-1, i]) / (Y[j+1, 0] - Y[j-1, 0])
                grad = np.array([grad_x, grad_y])
                
                # Update with momentum
                velocity = momentum * velocity - lr * grad
                pos += velocity
                
                # Add noise (temperature)
                pos += np.random.randn(2) * 0.05
                
                trajectory.append(pos.copy())
        
        trajectory = np.array(trajectory)
        
        # Plot trajectory
        ax.plot(trajectory[:, 0], trajectory[:, 1], 'r-', linewidth=2, 
               label='Optimization Path')
        ax.scatter(trajectory[0, 0], trajectory[0, 1], c='green', s=100, 
                  marker='o', label='Start', zorder=5)
        ax.scatter(trajectory[-1, 0], trajectory[-1, 1], c='red', s=100, 
                  marker='*', label='End', zorder=5)
        
        # Add arrows showing direction
        for i in range(0, len(trajectory)-1, 5):
            ax.annotate('', xy=trajectory[i+1], xytext=trajectory[i],
                       arrowprops=dict(arrowstyle='->', color='red', alpha=0.5))
        
        return trajectory
    
    def visualize_complete_landscape(self):
        """Create complete energy landscape visualization"""
        # Main 3D surface
        ax1 = self.fig.add_subplot(2, 2, 1, projection='3d')
        X, Y, Z = self.create_energy_surface(num_domains=5)
        
        surf = ax1.plot_surface(X, Y, Z, cmap='viridis', alpha=0.7,
                               linewidth=0, antialiased=True)
        ax1.set_xlabel('Semantic Dimension 1')
        ax1.set_ylabel('Semantic Dimension 2')
        ax1.set_zlabel('Energy')
        ax1.set_title('Complete Energy Landscape')
        
        # Contour plot with trajectory
        ax2 = self.fig.add_subplot(2, 2, 2)
        contour = ax2.contour(X, Y, Z, levels=20, cmap='viridis')
        ax2.clabel(contour, inline=True, fontsize=8)
        trajectory = self.plot_optimization_trajectory(ax2, X, Y, Z)
        ax2.set_xlabel('Semantic Dimension 1')
        ax2.set_ylabel('Semantic Dimension 2')
        ax2.set_title('Optimization Trajectory')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Energy evolution
        ax3 = self.fig.add_subplot(2, 2, 3)
        # Compute energy along trajectory
        energies = []
        for pos in trajectory:
            i = np.argmin(np.abs(X[0, :] - pos[0]))
            j = np.argmin(np.abs(Y[:, 0] - pos[1]))
            if 0 <= i < X.shape[1] and 0 <= j < Y.shape[0]:
                energies.append(Z[j, i])
        
        ax3.plot(energies, 'b-', linewidth=2)
        ax3.set_xlabel('Optimization Step')
        ax3.set_ylabel('Total Energy')
        ax3.set_title('Energy Evolution During Optimization')
        ax3.grid(True, alpha=0.3)
        
        # Force field visualization
        ax4 = self.fig.add_subplot(2, 2, 4)
        # Subsample for clarity
        skip = 10
        X_sub = X[::skip, ::skip]
        Y_sub = Y[::skip, ::skip]
        
        # Compute gradients (forces)
        Fy, Fx = np.gradient(Z[::skip, ::skip])
        
        # Normalize
        magnitude = np.sqrt(Fx**2 + Fy**2)
        Fx_norm = -Fx / (magnitude + 1e-10)
        Fy_norm = -Fy / (magnitude + 1e-10)
        
        ax4.quiver(X_sub, Y_sub, Fx_norm, Fy_norm, magnitude,
                  cmap='hot', scale=30, alpha=0.6)
        ax4.set_xlabel('Semantic Dimension 1')
        ax4.set_ylabel('Semantic Dimension 2')
        ax4.set_title('Force Field (Negative Gradient)')
        ax4.set_aspect('equal')
        
        plt.tight_layout()
        return self.fig

def create_architecture_diagram():
    """Create and display the architecture diagram"""
    diagram = ArchitectureDiagram()
    fig = diagram.draw_complete_architecture()
    return fig

def create_energy_landscape():
    """Create and display the energy landscape visualization"""
    visualizer = EnergyLandscapeVisualizer()
    fig = visualizer.visualize_complete_landscape()
    return fig

if __name__ == "__main__":
    # Test individual components
    fig1 = create_architecture_diagram()
    fig2 = create_energy_landscape()
    plt.show()
