import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

# --- Configuration Parameters ---
# This version uses the original single-vector attractor model.
D_CONCEPT = 2  # Dimensionality of concept vectors (2D for visualization)
N_CONCEPTS = 10 # Number of initial concepts
N_ATTRACTORS = 3 # Number of attractors (knowledge domains)

# Simulation parameters
N_STEPS = 200 # Total simulation steps
ETA = 0.05 # Learning rate / step size for motion
GRAPH_FORCE_STRENGTH = 0.02 # Strength of connections between concepts
ATTRACTOR_FORCE_STRENGTH = 0.005 # Strength of attractor pull (adjusted for different force model)

# Dynamic event thresholds from the architecture document
TAU_FISSION = 1.5 # Threshold for splitting a concept (based on velocity)
TAU_FUSION = 0.1 # Threshold for merging two concepts (based on distance)

class Concept:
    """Represents a single concept in the manifold."""
    def __init__(self, concept_id, position):
        self.id = concept_id
        self.pos = np.array(position, dtype=float)
        self.vel = np.zeros(D_CONCEPT)
        self.force = np.zeros(D_CONCEPT)

    def __repr__(self):
        return f"Concept({self.id}, pos={self.pos})"

class Attractor:
    """Represents a domain of knowledge as a single point attractor."""
    def __init__(self):
        # Initialize the attractor at a random position in the manifold.
        self.pos = np.random.rand(D_CONCEPT) * 12 - 6

    def calculate_force(self, position):
        """
        Calculates the force as a simple pull towards the attractor's position.
        """
        direction = self.pos - position
        distance_sq = np.sum(direction**2)
        if distance_sq < 0.1:
            return np.zeros(D_CONCEPT)
        
        force = direction / (distance_sq + 1e-6)
        return force

class Simulation:
    """Orchestrates the entire thought simulation process."""
    def __init__(self):
        self.concepts = {i: Concept(i, np.random.rand(D_CONCEPT) * 8 - 4) for i in range(N_CONCEPTS)}
        self.concept_graph = {i: [(i + 1) % N_CONCEPTS, (i - 1 + N_CONCEPTS) % N_CONCEPTS] for i in range(N_CONCEPTS)}
        self.attractors = [Attractor() for _ in range(N_ATTRACTORS)]
        self.next_concept_id = N_CONCEPTS

    def _apply_graph_forces(self):
        """Applies forces based on the relationships in the ConceptGraph."""
        for cid, concept in self.concepts.items():
            for neighbor_id in self.concept_graph.get(cid, []):
                if neighbor_id in self.concepts:
                    neighbor = self.concepts[neighbor_id]
                    direction = neighbor.pos - concept.pos
                    concept.force += direction * GRAPH_FORCE_STRENGTH

    def _apply_attractor_forces(self):
        """Applies forces from the single-vector attractors."""
        for concept in self.concepts.values():
            total_attractor_force = np.zeros(D_CONCEPT)
            for attractor in self.attractors:
                total_attractor_force += attractor.calculate_force(concept.pos)
            concept.force += total_attractor_force * ATTRACTOR_FORCE_STRENGTH

    def _update_positions(self):
        """Updates concept positions based on the total calculated force."""
        for concept in self.concepts.values():
            force_magnitude = np.linalg.norm(concept.force)
            if force_magnitude > 2.0:
                 concept.force = concept.force / force_magnitude * 2.0

            concept.vel = concept.force
            concept.pos += concept.vel * ETA
            concept.force = np.zeros(D_CONCEPT)

    def _handle_dynamic_events(self):
        """Handles Fission and Fusion events."""
        # Fission
        fission_events = []
        for cid, concept in list(self.concepts.items()):
            if np.linalg.norm(concept.vel) > TAU_FISSION:
                fission_events.append(cid)

        for cid in fission_events:
            if cid not in self.concepts: continue
            old_concept = self.concepts.pop(cid)
            new_id_1, new_id_2 = self.next_concept_id, self.next_concept_id + 1
            self.next_concept_id += 2
            self.concepts[new_id_1] = Concept(new_id_1, old_concept.pos + old_concept.vel * 0.1)
            self.concepts[new_id_2] = Concept(new_id_2, old_concept.pos - old_concept.vel * 0.1)
            print(f"FISSION: Concept {cid} split into {new_id_1} and {new_id_2}")

        # Fusion
        fused_ids = set()
        concepts_to_fuse = list(self.concepts.items())
        for i in range(len(concepts_to_fuse)):
            for j in range(i + 1, len(concepts_to_fuse)):
                cid1, c1 = concepts_to_fuse[i]
                cid2, c2 = concepts_to_fuse[j]
                if cid1 in fused_ids or cid2 in fused_ids: continue
                if np.linalg.norm(c1.pos - c2.pos) < TAU_FUSION:
                    new_id = self.next_concept_id
                    self.next_concept_id += 1
                    self.concepts[new_id] = Concept(new_id, (c1.pos + c2.pos) / 2)
                    if cid1 in self.concepts: self.concepts.pop(cid1)
                    if cid2 in self.concepts: self.concepts.pop(cid2)
                    fused_ids.add(cid1)
                    fused_ids.add(cid2)
                    print(f"FUSION: Concepts {cid1} and {cid2} merged into {new_id}")
                    return

    def step(self):
        """Performs a single step of the simulation."""
        self._apply_graph_forces()
        self._apply_attractor_forces()
        self._update_positions()
        self._handle_dynamic_events()

# --- Visualization Setup ---
sim = Simulation()
fig, ax = plt.subplots(figsize=(10, 10))
force_quiver = None

def setup_plot():
    """Sets up the plot aesthetics."""
    ax.clear()
    ax.set_xlim(-8, 8)
    ax.set_ylim(-8, 8)
    ax.set_aspect('equal', adjustable='box')
    ax.set_title("Continuum Simulation (Single-Vector Attractors - Fixed)")
    ax.grid(True, linestyle='--', alpha=0.6)

    attractor_positions = np.array([a.pos for a in sim.attractors])
    ax.scatter(attractor_positions[:, 0], attractor_positions[:, 1],
               s=400, c='gold', marker='*', edgecolor='black', zorder=3, label='Attractors')
    ax.legend()

concept_nodes = ax.scatter([], [], s=100, c='black', zorder=5)
step_text = ax.text(0.02, 0.95, '', transform=ax.transAxes)

def update(frame):
    """The main animation function, called for each frame."""
    global force_quiver
    sim.step()

    positions = np.array([c.pos for c in sim.concepts.values()]) if sim.concepts else np.empty((0, D_CONCEPT))
    forces = np.array([c.force for c in sim.concepts.values()]) if sim.concepts else np.empty((0, D_CONCEPT))

    concept_nodes.set_offsets(positions)

    if force_quiver:
        force_quiver.remove()

    if positions.size > 0:
        force_quiver = ax.quiver(positions[:, 0], positions[:, 1],
                                 forces[:, 0], forces[:, 1],
                                 color='r', alpha=0.7, zorder=4, width=0.005,
                                 scale=1.0, scale_units='xy')
    else:
        force_quiver = None

    step_text.set_text(f"Step: {frame+1}/{N_STEPS}\nConcepts: {len(sim.concepts)}")
    return [concept_nodes, force_quiver, step_text] if force_quiver else [concept_nodes, step_text]

# --- Run the Animation ---
setup_plot()
ani = FuncAnimation(fig, update, frames=N_STEPS, blit=False, interval=50, repeat=False)

try:
    print("Saving animation to continuum_simulation_single_vector_fixed.gif... This may take a moment.")
    ani.save('continuum_simulation_single_vector_fixed.gif', writer='imagemagick', fps=20)
    print("Animation saved successfully.")
except Exception as e:
    print(f"\nCould not save animation. Error: {e}")
    print("Please ensure you have ImageMagick installed and configured for Matplotlib.")
    print("Displaying animation inline instead.")
    plt.show()
