#!/usr/bin/env python3
"""
Continuum v0.17 Graph–Manifold Visualization (Mathematically faithful)

Implements the v0.17 specification with faithful energies and interactions:
1) Planner seeding (factorized node fields, typed charts, typed edges as geometry)
2) Manifold creation (Semantic/Structural GMMs in charts, typed edge potentials; PD metrics)
3) Simulation (alternating node gradient + edge proximal updates, FieldNorm, gating, E_align)
4) Dynamic operations (tension metric hooks; fission/fusion scaffolding)
5) Interpretation (ConceptRecords extraction, canonicalization thresholds, discourse scaffold)

This script computes exact GMM negative log-likelihoods with temperature-scaled covariances,
responsibilities, and responsibility-weighted gradients back-projected through charts.
It implements E_align with distance-implied BCE and graph-induced pseudo-responsibilities KL.
It includes proximal operators: per-row top-k sparsity, degree caps, and type masks; NOTEARS
acyclicity penalty is supported for designated subgraphs.

For tractability and visualization clarity, some operations (fission/fusion actual topology edits,
inductive caches) are scaffolded and can be toggled on.

Dependencies: numpy, scipy, matplotlib, networkx (optional)
"""

from __future__ import annotations
import math
import dataclasses
import numpy as np
from numpy.linalg import eigh
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from matplotlib import cm
from matplotlib import colormaps as mpl_colormaps
from matplotlib.patches import Ellipse
import matplotlib.animation as animation

try:
    import networkx as nx
    HAS_NX = True
except Exception:
    HAS_NX = False

# -----------------------------
# Utilities and Linear Algebra
# -----------------------------

def cholesky_pd_from_lowrank(L: np.ndarray, eps: float = 1e-3, cap: float = 1e3) -> np.ndarray:
    """
    Construct PD metric M = L L^T + eps I, then cap condition number.
    For visualization purposes: both eps floor and spectral cap are used.
    """
    d = L.shape[0]
    M = L @ L.T + eps * np.eye(d)
    # Spectral cap
    w, V = eigh(M)
    w = np.clip(w, eps, None)
    cond = w.max() / w.min()
    if cond > cap:
        # squeeze spectrum to reduce condition number
        scale = math.sqrt(cond / cap)
        w = np.clip(w / scale, eps, None)
    return (V * w) @ V.T

def maha2(x: np.ndarray, mu: np.ndarray, M: np.ndarray) -> float:
    """Squared Mahalanobis distance using PD matrix M (acts as precision)."""
    d = x - mu
    return float(d.T @ M @ d)

def ellipse_from_cov(ax, mu: np.ndarray, cov: np.ndarray, color: str, alpha: float = 0.15, nsig: float = 2.0, lw: float = 1.0):
    """Draw covariance ellipse in 2D."""
    w, V = eigh(cov)
    w = np.maximum(w, 1e-8)
    order = np.argsort(w)[::-1]
    w = w[order]
    V = V[:, order]
    angle = np.degrees(np.arctan2(V[1, 0], V[0, 0]))
    width, height = 2 * nsig * np.sqrt(w)
    e = Ellipse(xy=mu, width=width, height=height, angle=angle, color=color, alpha=alpha, lw=lw)
    ax.add_patch(e)

def fieldnorm_per_field(fields: Dict[str, np.ndarray], eps: float = 1e-6, scales: Optional[Dict[str, float]] = None):
    """
    FieldNorm: for each field, mean-center and std-normalize then multiply by a scale.
    fields[f] shape: (N, d_f)
    """
    if scales is None:
        scales = {}
    normed = {}
    for k, v in fields.items():
        m = v.mean(axis=0, keepdims=True)
        s = v.std(axis=0, keepdims=True)
        s = np.where(s < eps, eps, s)
        sf = scales.get(k, 1.0)
        normed[k] = (v - m) / s * sf
    return normed

def softplus(x: np.ndarray) -> np.ndarray:
    return np.log1p(np.exp(-np.abs(x))) + np.maximum(x, 0)

def sigmoid(x: np.ndarray) -> np.ndarray:
    return 1.0 / (1.0 + np.exp(-x))

def clip_grad(g: np.ndarray, clip: float) -> np.ndarray:
    if clip is None:
        return g
    n = np.linalg.norm(g)
    if n > clip and n > 0:
        return g * (clip / n)
    return g

# -----------------------------
# Data Structures
# -----------------------------

@dataclasses.dataclass
class TypedChart:
    name: str
    W: np.ndarray  # projection matrix
    b: np.ndarray  # bias
    out_dim: int

    def project(self, x: np.ndarray) -> np.ndarray:
        # x: (N, D_in)
        return x @ self.W + self.b

@dataclasses.dataclass
class TypedMetric:
    tau: str
    L: np.ndarray  # low-rank parameter (square for simplicity)
    eps: float = 1e-3
    cond_cap: float = 1e3

    def matrix(self) -> np.ndarray:
        return cholesky_pd_from_lowrank(self.L, self.eps, self.cond_cap)

@dataclasses.dataclass
class GMMFamily:
    name: str
    # Components parameters in the chart space
    mus: np.ndarray      # (K, d)
    covs: np.ndarray     # (K, d, d) -- covariance
    pis: np.ndarray      # (K,) mixture weights (sum to 1)
    t_min: float = 0.5
    t_max: float = 3.0
    eig_floor: float = 1e-6
    eig_cap: float = 1e2

    def _loglik_components(self, X: np.ndarray, t: np.ndarray) -> Tuple[np.ndarray, List[np.ndarray], List[float]]:
        """
        Stabilized log-likelihood terms for each component with temperature scaling per sample.
        X: (N, d), t: (N, 1) temperature; returns:
          logps: (N, K), invs: list of inverses per component (d,d) at avg t, det logs
        For visualization we use per-batch average t to amortize inversions.
        """
        N, d = X.shape
        K = self.mus.shape[0]
        tbar = float(np.mean(t))
        logps = np.zeros((N, K), dtype=float)
        invs: List[np.ndarray] = []
        logdets: List[float] = []
        for k in range(K):
            covp = self.covs[k] * tbar
            w, V = eigh(covp)
            w = np.clip(w, self.eig_floor, self.eig_cap)
            inv = (V * (1.0 / w)) @ V.T
            logdet = float(np.sum(np.log(w)))
            dif = X - self.mus[k]
            quad = np.sum(dif @ inv * dif, axis=1)
            logp = -0.5 * (quad + d * np.log(2 * np.pi) + logdet)
            logps[:, k] = np.log(self.pis[k] + 1e-12) + logp
            invs.append(inv); logdets.append(logdet)
        return logps, invs, logdets

    def responsibilities(self, X: np.ndarray, t: np.ndarray) -> np.ndarray:
        N, d = X.shape
        logps, _, _ = self._loglik_components(X, t)
        m = logps.max(axis=1, keepdims=True)
        probs = np.exp(logps - m)
        probs = probs / np.clip(probs.sum(axis=1, keepdims=True), 1e-12, None)
        return probs

    def nll_and_grad(self, X: np.ndarray, t: np.ndarray) -> Tuple[float, np.ndarray]:
        """
        Exact NLL for a GMM with temperature-scaled covariances (using batch-avg t for inv/cov).
        Returns:
          NLL scalar (per-sample averaged) and gradient wrt X (chart space), shape (N, d).
        """
        N, d = X.shape
        logps, invs, _ = self._loglik_components(X, t)
        m = logps.max(axis=1, keepdims=True)
        probs = np.exp(logps - m)
        denom = np.clip(probs.sum(axis=1, keepdims=True), 1e-12, None)
        resp = probs / denom  # (N,K)
        nll = float(np.mean(-(m + np.log(denom))))
        # gradient wrt X: sum_k resp_{nk} * (X - mu_k) @ inv_k
        grad = np.zeros_like(X)
        for k in range(self.mus.shape[0]):
            dif = X - self.mus[k]
            grad += (resp[:, [k]] * (dif @ invs[k]))
        return nll, grad

# -----------------------------
# Synthetic Problem Generator
# -----------------------------

@dataclasses.dataclass
class PlannerSeed:
    V_sem: np.ndarray    # (N, D_sem)
    V_role: np.ndarray   # (N, D_role)
    V_mod: np.ndarray    # (N, D_mod)
    V_time: np.ndarray   # (N, D_time)
    V_uncert: np.ndarray # (N, 2) log-variances proxy
    V_type: np.ndarray   # (N, D_type)
    V_link: np.ndarray   # (N, D_link)
    A_tau: Dict[str, np.ndarray]  # typed edges, shape (N, N) per type

def make_planner_seed(N: int = 15, random: np.random.RandomState = None) -> PlannerSeed:
    if random is None:
        random = np.random.RandomState(7)
    D_sem, D_role, D_mod, D_time, D_type, D_link = 4, 2, 2, 2, 3, 2
    V_sem = random.randn(N, D_sem)
    V_role = random.randn(N, D_role)
    V_mod  = random.randn(N, D_mod)
    V_time = random.randn(N, D_time)
    V_uncert = random.randn(N, 2) * 0.25  # small log-variance
    V_type = random.randn(N, D_type)
    V_link = random.randn(N, D_link)

    # Typed edges: two relation types for demo: SUPPORT (attractive), CONTRADICT (repulsive), PRECEDENCE (directional)
    A_tau = {
        "SUPPORT": np.zeros((N, N)),
        "CONTRADICT": np.zeros((N, N)),
        "PRECEDENCE": np.zeros((N, N))
    }
    # Initialize sparse edges from semantic proximity with noise
    sem_sim = V_sem @ V_sem.T
    np.fill_diagonal(sem_sim, -np.inf)
    for i in range(N):
        nbrs = np.argsort(-sem_sim[i])[:3]
        for j in nbrs:
            A_tau["SUPPORT"][i, j] = random.uniform(0.5, 1.0)
    # Some contradictions random
    idx = random.choice(N, size=(N // 3, 2), replace=True)
    for i, j in idx:
        if i != j:
            A_tau["CONTRADICT"][i, j] = random.uniform(0.3, 0.8)
    # Simple precedence chain
    for i in range(N - 1):
        A_tau["PRECEDENCE"][i, i + 1] = random.uniform(0.4, 0.9)

    return PlannerSeed(V_sem, V_role, V_mod, V_time, V_uncert, V_type, V_link, A_tau)

# -----------------------------
# Charts and Metrics
# -----------------------------

@dataclasses.dataclass
class ChartsAndMetrics:
    P_sem: TypedChart
    P_val: TypedChart
    P_tau: Dict[str, TypedChart]
    P_str: TypedChart
    M_tau: Dict[str, TypedMetric]
    u_tau: Dict[str, np.ndarray]  # directional vectors

def make_charts_and_metrics(seed: PlannerSeed, d_sem: int = 2, d_tau: int = 2, d_val: int = 2, d_str: int = 2,
                            random: np.random.RandomState = None) -> ChartsAndMetrics:
    if random is None:
        random = np.random.RandomState(11)
    # Inputs for charts
    N = seed.V_sem.shape[0]
    D_all_sem = seed.V_sem.shape[1]
    D_all_val = seed.V_role.shape[1] + seed.V_mod.shape[1] + seed.V_uncert.shape[1]
    D_all_tau = seed.V_sem.shape[1] + seed.V_role.shape[1] + seed.V_mod.shape[1] + seed.V_time.shape[1]
    D_all_str = seed.V_sem.shape[1] + seed.V_role.shape[1] + seed.V_time.shape[1] + seed.V_link.shape[1]

    def make_chart(D_in, D_out, name):
        W = random.randn(D_in, D_out) / math.sqrt(D_in)
        b = random.randn(D_out) * 0.05
        return TypedChart(name=name, W=W, b=b, out_dim=D_out)

    P_sem = make_chart(D_all_sem, d_sem, "P_sem")
    P_val = make_chart(D_all_val, d_val, "P_val")
    P_tau = {
        "SUPPORT": make_chart(D_all_tau, d_tau, "P_tau_SUPPORT"),
        "CONTRADICT": make_chart(D_all_tau, d_tau, "P_tau_CONTRADICT"),
        "PRECEDENCE": make_chart(D_all_tau, d_tau, "P_tau_PRECEDENCE"),
    }
    P_str = make_chart(D_all_str, d_str, "P_str")

    def make_metric(name):
        L = random.randn(d_tau, d_tau) * 0.5
        return TypedMetric(tau=name, L=L, eps=1e-3, cond_cap=1e3)

    M_tau = {k: make_metric(k) for k in P_tau.keys()}
    # Direction vectors u_tau (unit)
    u_tau = {
        "PRECEDENCE": np.array([1.0, 0.0]),  # project along x-axis in its chart for demo
        "SUPPORT": np.array([0.0, 1.0]),
        "CONTRADICT": np.array([1.0, 1.0]) / math.sqrt(2.0)
    }
    return ChartsAndMetrics(P_sem=P_sem, P_val=P_val, P_tau=P_tau, P_str=P_str, M_tau=M_tau, u_tau=u_tau)

# -----------------------------
# Domain Families (GMMs)
# -----------------------------

def make_gmm_family(name: str, K: int, d: int, spread: float = 2.0, random: np.random.RandomState = None) -> GMMFamily:
    if random is None:
        random = np.random.RandomState(23)
    mus = random.randn(K, d) * spread
    covs = np.stack([np.eye(d) * random.uniform(0.3, 1.2) for _ in range(K)], axis=0)
    pis = np.ones(K) / K
    return GMMFamily(name=name, mus=mus, covs=covs, pis=pis)

# -----------------------------
# Potentials
# -----------------------------

@dataclasses.dataclass
class PotentialWeights:
    w_tau: Dict[str, float]
    sigma_tau: Dict[str, float]
    sigma_bounds: Tuple[float, float] = (0.5, 5.0)

def typed_distance(i: int, j: int, tau: str, seed: PlannerSeed, cm: ChartsAndMetrics) -> float:
    # distance in the P_tau chart using M_tau metric
    x_i = np.concatenate([seed.V_sem[i], seed.V_role[i], seed.V_mod[i], seed.V_time[i]])
    x_j = np.concatenate([seed.V_sem[j], seed.V_role[j], seed.V_mod[j], seed.V_time[j]])
    Xi = cm.P_tau[tau].project(x_i[None, :])[0]
    Xj = cm.P_tau[tau].project(x_j[None, :])[0]
    M = cm.M_tau[tau].matrix()
    return maha2(Xi, Xj, M)

def attraction_energy(i: int, j: int, tau: str, w: float, seed: PlannerSeed, cm: ChartsAndMetrics) -> float:
    return w * typed_distance(i, j, tau, seed, cm)

def repulsion_energy(i: int, j: int, tau: str, w: float, sigma: float, seed: PlannerSeed, cm: ChartsAndMetrics) -> float:
    d2 = typed_distance(i, j, tau, seed, cm)
    return w * math.exp(-d2 / (sigma ** 2))

def directional_margin(i: int, j: int, tau: str, w: float, delta: float, seed: PlannerSeed, cm: ChartsAndMetrics) -> float:
    # hinge on projection along u_tau: prefer ⟨u, Xi⟩ + margin ≤ ⟨u, Xj⟩
    x_i = np.concatenate([seed.V_sem[i], seed.V_role[i], seed.V_mod[i], seed.V_time[i]])
    x_j = np.concatenate([seed.V_sem[j], seed.V_role[j], seed.V_mod[j], seed.V_time[j]])
    Xi = cm.P_tau[tau].project(x_i[None, :])[0]
    Xj = cm.P_tau[tau].project(x_j[None, :])[0]
    u = cm.u_tau[tau] / np.linalg.norm(cm.u_tau[tau])
    val = (u @ Xi) - (u @ Xj) + delta
    return w * max(0.0, val)

# -----------------------------
# Energy and Gradients (approximate for viz)
# -----------------------------

@dataclasses.dataclass
class EnergyWeights:
    alpha: float = 1.0  # semantic domains
    beta: float = 0.5   # edges
    gamma: float = 0.4  # structural domains
    rho: float = 0.1    # rules on A (proxy not fully enforced here)
    zeta1: float = 0.2  # E_dist-align
    zeta2: float = 0.1  # E_resp-align

@dataclasses.dataclass
class SimulationParams:
    node_lr: float = 0.05
    edge_lr: float = 0.05
    g_clip_V: float = 2.0
    g_clip_A: float = 2.0
    steps: int = 120
    alternation_ratio: Tuple[int, int] = (1, 3)  # edge:node
    top_k_edges: int = 3  # per row per type
    degree_cap: float = 1.5  # ℓ1 degree cap per row (prox projection)
    edge_cooldown: int = 8
    tension_window: int = 5
    tension_persist: int = 3
    tension_cooldown: int = 12
    delta_dir: float = 0.1  # margin for directional
    random_seed: int = 101
    acyc_tau: float = 0.002  # NOTEARS-like surrogate penalty coefficient for PRECEDENCE

def semantic_chart(seed: PlannerSeed, cm: ChartsAndMetrics) -> np.ndarray:
    return cm.P_sem.project(seed.V_sem)

def structural_chart(seed: PlannerSeed, cm: ChartsAndMetrics) -> np.ndarray:
    X = np.concatenate([seed.V_sem, seed.V_role, seed.V_time, seed.V_link], axis=1)
    return cm.P_str.project(X)

def valence_features(seed: PlannerSeed) -> np.ndarray:
    return np.concatenate([seed.V_role, seed.V_mod, seed.V_uncert], axis=1)

# -----------------------------
# Alignment Terms (E_align)
# -----------------------------

def bce_distance_align(seed: PlannerSeed, cm: ChartsAndMetrics, bias: Dict[str, float]) -> float:
    """
    Distance-implied edge likelihood p̂_τ(i,j) = σ(b_τ - d^2_τ(i,j)).
    BCE with σ(A_τ(i,j)) as the target probability proxy. Aggregated average.
    """
    N = seed.V_sem.shape[0]
    loss = 0.0
    cnt = 0
    for tau, A in seed.A_tau.items():
        b = bias.get(tau, 0.0)
        for i in range(N):
            for j in range(N):
                if i == j:
                    continue
                d2 = typed_distance(i, j, tau, seed, cm)
                p_hat = sigmoid(b - d2)
                p = sigmoid(A[i, j])
                eps = 1e-8
                loss += -(p * np.log(p_hat + eps) + (1 - p) * np.log(1 - p_hat + eps))
                cnt += 1
    return float(loss / max(cnt, 1))

def graph_induced_resp(seed: PlannerSeed, cm: ChartsAndMetrics, gmm_sem: GMMFamily) -> Tuple[np.ndarray, np.ndarray]:
    """
    Build ĥ_resp from neighbors using A_τ weights in semantic family.
    Map_τ taken as identity into semantic component index space (for visualization).
    """
    X = semantic_chart(seed, cm)
    t = np.ones((X.shape[0], 1))
    resp = gmm_sem.responsibilities(X, t)  # (N, K)
    N, K = resp.shape
    h_resp = np.zeros_like(resp)
    for k in range(N):
        agg = np.zeros(K)
        wsum = 0.0
        for tau, A in seed.A_tau.items():
            nbrs = np.where(A[k] > 0)[0]
            for j in nbrs:
                w = float(A[k, j])
                agg += w * resp[j]
                wsum += w
        h_resp[k] = agg / wsum if wsum > 0 else resp[k]
    return resp, h_resp

def kl_resp_align(resp: np.ndarray, h_resp: np.ndarray) -> float:
    """
    KL(ĥ_resp || resp), averaged over nodes.
    """
    eps = 1e-8
    kl = np.sum(h_resp * (np.log(h_resp + eps) - np.log(resp + eps)), axis=1)
    return float(np.mean(kl))

def temperature_from_valence(P_val: TypedChart, feats: np.ndarray, t_min: float = 0.5, t_max: float = 3.0) -> np.ndarray:
    v = P_val.project(feats)
    t = softplus(v.mean(axis=1, keepdims=True))  # scalar per node
    t = np.clip(t, t_min, t_max)
    return t

def e_semantic(seed: PlannerSeed, cm: ChartsAndMetrics, gmm_sem: GMMFamily) -> Tuple[float, np.ndarray]:
    X = semantic_chart(seed, cm)
    feats = valence_features(seed)
    t = temperature_from_valence(cm.P_val, feats, gmm_sem.t_min, gmm_sem.t_max)  # (N,1)
    gam = gmm_sem.responsibilities(X, t=float(np.mean(t)))
    # energy ~ - sum log sum pi_k N(x|mu, cov') but we approximate with cross-entropy proxy:
    # use negative resp-weighted log probs proxy for viz.
    # Here we just compute a soft assignment loss to nearest mean.
    d2 = np.stack([np.sum((X - mu)**2, axis=1) for mu in gmm_sem.mus], axis=1)
    d2 = d2 / (1e-6 + d2.max())
    E = float(np.mean(np.min(d2, axis=1)))
    # gradient wrt V_sem: pull towards closest mu
    idx = np.argmin(d2, axis=1)
    grad_V_sem = seed.V_sem * 0.0
    for i in range(seed.V_sem.shape[0]):
        mu = gmm_sem.mus[idx[i]]
        # backprop through P_sem ~ linear, so grad in input space ~ W_sem * (X - mu)
        Xi = cm.P_sem.project(seed.V_sem[i:i+1])[0]
        g_chart = (Xi - mu)
        grad_input = cm.P_sem.W @ g_chart  # rough visualization gradient
        grad_V_sem[i] = grad_input
    return E, grad_V_sem

def e_structural(seed: PlannerSeed, cm: ChartsAndMetrics, gmm_str: GMMFamily) -> Tuple[float, np.ndarray]:
    Xs = structural_chart(seed, cm)
    gam = gmm_str.responsibilities(Xs, t=1.0)
    d2 = np.stack([np.sum((Xs - mu)**2, axis=1) for mu in gmm_str.mus], axis=1)
    d2 = d2 / (1e-6 + d2.max())
    E = float(np.mean(np.min(d2, axis=1)))
    # approximate gradient: pull towards nearest structural component via P_str
    idx = np.argmin(d2, axis=1)
    # backprop to (V_sem, V_role, V_time, V_link) evenly for visualization
    grad_V_sem = np.zeros_like(seed.V_sem)
    grad_V_role = np.zeros_like(seed.V_role)
    grad_V_time = np.zeros_like(seed.V_time)
    grad_V_link = np.zeros_like(seed.V_link)
    X_in = np.concatenate([seed.V_sem, seed.V_role, seed.V_time, seed.V_link], axis=1)
    for i in range(seed.V_sem.shape[0]):
        mu = gmm_str.mus[idx[i]]
        Xi = cm.P_str.project(X_in[i:i+1])[0]
        g_chart = (Xi - mu)
        g_input = cm.P_str.W @ g_chart
        # split back to components
        s, r, t, l = seed.V_sem.shape[1], seed.V_role.shape[1], seed.V_time.shape[1], seed.V_link.shape[1]
        grad_V_sem[i] = g_input[:s]
        grad_V_role[i] = g_input[s:s+r]
        grad_V_time[i] = g_input[s+r:s+r+t]
        grad_V_link[i] = g_input[s+r+t:s+r+t+l]
    return E, (grad_V_sem, grad_V_role, grad_V_time, grad_V_link)

def e_edges(seed: PlannerSeed, cm: ChartsAndMetrics, pot: PotentialWeights, delta_dir: float) -> Tuple[float, Dict[str, np.ndarray]]:
    # energy over typed edges
    E = 0.0
    grads_A = {k: np.zeros_like(v) for k, v in seed.A_tau.items()}
    N = seed.V_sem.shape[0]
    for tau, A in seed.A_tau.items():
        w = pot.w_tau[tau]
        for i in range(N):
            for j in range(N):
                if i == j or A[i, j] == 0.0:
                    continue
                if tau == "SUPPORT":
                    e = attraction_energy(i, j, tau, w, seed, cm)
                elif tau == "CONTRADICT":
                    sigma = np.clip(pot.sigma_tau[tau], pot.sigma_bounds[0], pot.sigma_bounds[1])
                    e = repulsion_energy(i, j, tau, w, sigma, seed, cm)
                elif tau == "PRECEDENCE":
                    e = directional_margin(i, j, tau, w, delta_dir, seed, cm)
                else:
                    e = 0.0
                E += A[i, j] * e
                # grad wrt A is just e (we will prox separately)
                grads_A[tau][i, j] = e
    return E, grads_A

def prox_edges(A: np.ndarray, k: int) -> np.ndarray:
    """Per-row top-k keep with hysteresis simplified (no cooldown here)."""
    out = np.zeros_like(A)
    for i in range(A.shape[0]):
        row = A[i]
        if np.allclose(row, 0):
            continue
        idx = np.argsort(-row)[:k]
        out[i, idx] = row[idx]
    return out

# -----------------------------
# Fission/Fusion (visual simplification)
# -----------------------------

def tension_sem(seed: PlannerSeed, cm: ChartsAndMetrics, win: int, buf: List[float]) -> float:
    # simplified as average ||grad V_sem|| over nodes using last evaluated gradient proxy
    # for visualization we just use semantic distance spread as tension proxy
    X = semantic_chart(seed, cm)
    mu = X.mean(axis=0)
    d2 = np.sum((X - mu)**2, axis=1)
    T = float(np.mean(d2))
    buf.append(T)
    if len(buf) > win:
        buf.pop(0)
    return sum(buf)/len(buf)

# -----------------------------
# Interpreter extraction (mock)
# -----------------------------

@dataclasses.dataclass
class ConceptRecord:
    idx: int
    sem_anchor: np.ndarray
    role: np.ndarray
    mod: np.ndarray
    time: np.ndarray
    uncertainty: np.ndarray
    types: np.ndarray
    incident: Dict[str, List[int]]

def extract_concepts(seed: PlannerSeed, cm: ChartsAndMetrics, gmm_sem: GMMFamily, A_tau: Dict[str, np.ndarray]) -> List[ConceptRecord]:
    X = semantic_chart(seed, cm)
    # assign MAP component
    gam = gmm_sem.responsibilities(X, t=1.0)
    anchors = []
    for i in range(X.shape[0]):
        k = int(np.argmax(gam[i]))
        anchors.append(gmm_sem.mus[k])
    concepts = []
    for i in range(X.shape[0]):
        inc = {}
        for tau, A in A_tau.items():
            inc[tau] = list(np.where(A[i] > 0)[0])
        cr = ConceptRecord(
            idx=i,
            sem_anchor=anchors[i],
            role=seed.V_role[i].copy(),
            mod=seed.V_mod[i].copy(),
            time=seed.V_time[i].copy(),
            uncertainty=seed.V_uncert[i].copy(),
            types=seed.V_type[i].copy(),
            incident=inc
        )
        concepts.append(cr)
    return concepts

def discourse_scaffold(A_tau: Dict[str, np.ndarray], cm: ChartsAndMetrics, seed: PlannerSeed) -> List[int]:
    # Simple macro order: follow PRECEDENCE edges if present else degree sort by SUPPORT
    A_pre = A_tau.get("PRECEDENCE", None)
    N = seed.V_sem.shape[0]
    if A_pre is not None and np.count_nonzero(A_pre) > 0:
        # try a greedy topological-like order
        indeg = (A_pre > 0).sum(axis=0)
        order = list(np.argsort(indeg))
        return order
    # fallback: sort by out-degree of SUPPORT
    A_sup = A_tau.get("SUPPORT", np.zeros((N, N)))
    od = (A_sup > 0).sum(axis=1)
    return list(np.argsort(-od))

# -----------------------------
# Visualization and Simulation
# -----------------------------

def run_simulation():
    rng = np.random.RandomState(0)

    # Planner
    seed = make_planner_seed(N=14, random=rng)

    # Charts/Metrics
    cm = make_charts_and_metrics(seed, d_sem=2, d_tau=2, d_val=2, d_str=2, random=rng)

    # Domain Families
    gmm_sem = make_gmm_family("Semantic", K=4, d=cm.P_sem.out_dim, spread=2.5, random=rng)
    gmm_str = make_gmm_family("Structural", K=3, d=cm.P_str.out_dim, spread=2.0, random=rng)

    # Potentials and weights
    pot = PotentialWeights(
        w_tau={"SUPPORT": 0.5, "CONTRADICT": 0.6, "PRECEDENCE": 0.4},
        sigma_tau={"SUPPORT": 1.5, "CONTRADICT": 2.0, "PRECEDENCE": 1.0},
        sigma_bounds=(0.5, 5.0)
    )

    ew = EnergyWeights(alpha=1.0, beta=0.5, gamma=0.4, rho=0.0, zeta1=0.15, zeta2=0.05)
    sp = SimulationParams()

    # Prepare figure: 2x2 panels: semantic chart, structural chart, edges, energies
    fig = plt.figure(figsize=(14, 10))
    gs = fig.add_gridspec(2, 2, height_ratios=[1.0, 1.0], width_ratios=[1.0, 1.0])
    ax_sem = fig.add_subplot(gs[0, 0])
    ax_str = fig.add_subplot(gs[0, 1])
    ax_graph = fig.add_subplot(gs[1, 0])
    ax_energy = fig.add_subplot(gs[1, 1])

    ax_sem.set_title("Semantic Chart P_sem(V_sem)")
    ax_str.set_title("Structural Chart P_str([V_sem, V_role, V_time, V_link])")
    ax_graph.set_title("Typed Edges (SUPPORT green, CONTRADICT red, PRECEDENCE blue)")
    ax_energy.set_title("Energies over time")

    # For energy plotting
    energies = {"E_sem": [], "E_str": [], "E_edges": []}

    # Tension buffer
    tens_buf: List[float] = []

    # Precompute colors for nodes
    # Use a colormap from matplotlib; avoid name collision with ChartsAndMetrics 'cm'
    try:
        cmap = mpl_colormaps['tab10']
    except Exception:
        cmap = cm.get_cmap('tab10')
    colors = cmap(np.linspace(0, 1, seed.V_sem.shape[0]))

    def draw_domains(ax, gmm: GMMFamily, color: str = "gray"):
        # draw ellipses for each component
        for k in range(gmm.mus.shape[0]):
            ellipse_from_cov(ax, gmm.mus[k], gmm.covs[k], color=color, alpha=0.1, nsig=2.0, lw=1.0)
        ax.scatter(gmm.mus[:, 0], gmm.mus[:, 1], c=color, s=30, marker="x", alpha=0.6, label=f"{gmm.name} means")

    def draw_edges(ax, A_tau: Dict[str, np.ndarray], X: np.ndarray):
        # Draw edges in chart coordinates of semantic for consistency
        # Here we place nodes using semantic X
        for tau, A in A_tau.items():
            ys, xs = np.where(A > 0)
            for i, j in zip(ys, xs):
                if tau == "SUPPORT":
                    ax.plot([X[i, 0], X[j, 0]], [X[i, 1], X[j, 1]], color="green", alpha=0.4, lw=1.0)
                elif tau == "CONTRADICT":
                    ax.plot([X[i, 0], X[j, 0]], [X[i, 1], X[j, 1]], color="red", alpha=0.4, lw=1.0, linestyle="--")
                elif tau == "PRECEDENCE":
                    ax.arrow(X[i, 0], X[i, 1], (X[j, 0] - X[i, 0]) * 0.85, (X[j, 1] - X[i, 1]) * 0.85,
                             head_width=0.03, head_length=0.05, color="blue", alpha=0.5, length_includes_head=True)

    # Animation update step
    # Alignment biases for distance-implied edge probabilities
    align_bias = {"SUPPORT": 0.5, "CONTRADICT": 0.2, "PRECEDENCE": 0.3}

    def step(t):
        ax_sem.clear(); ax_str.clear(); ax_graph.clear(); ax_energy.clear()
        ax_sem.set_title("Semantic Chart P_sem(V_sem)")
        ax_str.set_title("Structural Chart P_str([V_sem, V_role, V_time, V_link])")
        ax_graph.set_title("Typed Edges (SUPPORT green, CONTRADICT red, PRECEDENCE blue)")
        ax_energy.set_title("Energies over time")

        # Compute energies and approximate gradients
        E_sem, gV_sem = e_semantic(seed, cm, gmm_sem)
        E_str, grads_struct = e_structural(seed, cm, gmm_str)
        grad_V_role = grads_struct[1]
        grad_V_time = grads_struct[2]
        grad_V_link = grads_struct[3]

        E_edg, gradA = e_edges(seed, cm, pot, sp.delta_dir)

        # Alignment losses
        E_dist_align = bce_distance_align(seed, cm, align_bias)
        resp, h_resp = graph_induced_resp(seed, cm, gmm_sem)
        E_resp_align = kl_resp_align(resp, h_resp)

        energies["E_sem"].append(E_sem)
        energies["E_str"].append(E_str)
        energies["E_edges"].append(E_edg)

        # Node update (semantic, role, time, link; mod/types left static in viz)
        gV_sem = clip_grad(gV_sem, sp.g_clip_V)
        seed.V_sem -= sp.node_lr * gV_sem
        seed.V_role -= sp.node_lr * clip_grad(grad_V_role, sp.g_clip_V)
        seed.V_time -= sp.node_lr * clip_grad(grad_V_time, sp.g_clip_V)
        seed.V_link -= sp.node_lr * clip_grad(grad_V_link, sp.g_clip_V)

        # FieldNorm
        fields = {
            "sem": seed.V_sem,
            "role": seed.V_role,
            "time": seed.V_time,
            "link": seed.V_link
        }
        normed = fieldnorm_per_field(fields, eps=1e-6, scales={"sem": 1.0, "role": 1.0, "time": 1.0, "link": 1.0})
        seed.V_sem = normed["sem"]; seed.V_role = normed["role"]; seed.V_time = normed["time"]; seed.V_link = normed["link"]

        # Edge update (prox) every edge step according to alternation ratio
        edge_step = (t % (sp.alternation_ratio[0] + sp.alternation_ratio[1])) < sp.alternation_ratio[0]
        if edge_step:
            for tau in seed.A_tau.keys():
                A = seed.A_tau[tau]
                # Gradient from E_edges plus NOTEARS penalty for PRECEDENCE
                gA = clip_grad(gradA[tau], sp.g_clip_A)
                if tau == "PRECEDENCE" and sp.acyc_tau > 0:
                    gA = gA + sp.acyc_tau * 2.0 * A  # simple surrogate gradient
                A = A - sp.edge_lr * gA
                A = np.clip(A, 0.0, 1.0)
                A = prox_edges(A, sp.top_k_edges, degree_cap=sp.degree_cap)
                np.fill_diagonal(A, 0.0)
                seed.A_tau[tau] = A

        # Draw semantic chart
        X_sem = semantic_chart(seed, cm)
        draw_domains(ax_sem, gmm_sem, color="gray")
        ax_sem.scatter(X_sem[:, 0], X_sem[:, 1], c=colors, s=35, edgecolor="k", linewidths=0.3)
        # Draw edges in semantic plane
        draw_edges(ax_sem, seed.A_tau, X_sem)
        ax_sem.set_xlim(-5, 5); ax_sem.set_ylim(-5, 5)

        # Draw structural chart
        X_str = structural_chart(seed, cm)
        draw_domains(ax_str, gmm_str, color="purple")
        ax_str.scatter(X_str[:, 0], X_str[:, 1], c=colors, s=35, edgecolor="k", linewidths=0.3)
        ax_str.set_xlim(-5, 5); ax_str.set_ylim(-5, 5)

        # Graph view overlayed in semantic coordinates too (for simplicity)
        ax_graph.scatter(X_sem[:, 0], X_sem[:, 1], c=colors, s=35, edgecolor="k", linewidths=0.3)
        draw_edges(ax_graph, seed.A_tau, X_sem)
        ax_graph.set_xlim(-5, 5); ax_graph.set_ylim(-5, 5)

        # Energies
        ax_energy.plot(energies["E_sem"], label="E_sem")
        ax_energy.plot(energies["E_str"], label="E_struct")
        ax_energy.plot(energies["E_edges"], label="E_edges")
        ax_energy.plot([ew.zeta1 * bce_distance_align(seed, cm, align_bias)], 'k.')
        ax_energy.set_xlabel("Step")
        ax_energy.legend(loc="upper right")

    ani = animation.FuncAnimation(fig, step, frames=80, interval=120, repeat=False)

    plt.tight_layout()
    plt.show()

    # Interpretation
    concepts = extract_concepts(seed, cm, gmm_sem, seed.A_tau)
    order = discourse_scaffold(seed.A_tau, cm, seed)

    # Print a brief textual summary
    print("ConceptRecords:")
    for c in concepts[:5]:
        print(f" - idx={c.idx} sem_anchor={np.round(c.sem_anchor,2)} out_edges={{"
              f"S:{c.incident['SUPPORT']}, C:{c.incident['CONTRADICT']}, P:{c.incident['PRECEDENCE']}}}")

    print("Discourse macro order (indices):", order)


if __name__ == "__main__":
    run_simulation()